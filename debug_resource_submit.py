"""
快速调试资源提交功能
"""
import asyncio
import json
import sys
import time

from resource_submitters.resource_submit_manager import ResourceSubmitManager
from utils import custom_print


async def debug_submit_resource(test_url: str = None) -> None:
    """
    快速调试资源提交功能

    Args:
        test_url: 测试URL，如果为None则使用默认测试URL
    """
    custom_print("=" * 50)
    custom_print("开始调试资源提交功能")
    custom_print("=" * 50)

    # 创建测试配置
    test_config = {
        "enabled": True,
        "auto_submit": True,
        "submitters": ["krzb", "yunso"],
        "submit_delay": {
            "min": 0.5,
            "max": 1
        },
        "avoid_duplicate": True,  # 避免重复提交
        "krzb": {
            "enabled": True,
            "api_url": "https://fc-resource-node-api.krzb.net",
            "batch_size": 4,
            "use_proxy": False,
            "headers": {
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json"
            }
        },
        "yunso": {
            "enabled": True,
            "api_url": "https://www.yunso.net/api/validate/dataentry.html",
            "login_url": "https://dash.yunso.net/index/user/login.html",
            "dataentry_url": "https://www.yunso.net/index/user/dataentry",
            "check_login_url": "https://www.yunso.net/index/user/index.html",
            "batch_size": 5,
            "use_proxy": False,
            "publicity": "1",
            "common_headers": {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "DNT": "1"
            },
            "submit_headers": {
                "accept": "*/*",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "priority": "u=1, i",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest"
            },
            "login_headers": {
                "accept": "application/json, text/javascript, */*; q=0.01",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "priority": "u=1, i",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest"
            },
            "login_page_headers": {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1"
            },
            "login_credentials": {
                "url": "https://dash.yunso.net/index/user/index.html",
                "account": "albert",
                "password": "xWumeC47q9LX2LF",
                "keeplogin": "1"
            }
        }
    }

    # 创建资源提交管理器
    resource_submit_manager = ResourceSubmitManager(test_config)

    # 测试URL
    if not test_url:
        test_url = "https://pan.quark.cn/s/3e9d2c123456"  # 示例URL

    test_links = [test_url]

    # 提交测试链接
    custom_print(f"准备提交测试链接: {test_url}")

    # 记录开始时间
    start_time = time.time()

    # 提交链接
    result = await resource_submit_manager.submit_links(test_links)

    # 记录结束时间
    end_time = time.time()
    elapsed_time = end_time - start_time

    try:
        # 输出结果
        custom_print("=" * 50)
        custom_print(f"提交结果 (耗时: {elapsed_time:.2f}秒):")
        custom_print(f"状态码: {result.get('code')}")
        custom_print(f"消息: {result.get('message')}")

        # 输出详细信息
        details = result.get("details", {})
        for submitter_type, submitter_result in details.items():
            custom_print(f"\n{submitter_type} 提交器结果:")
            custom_print(f"  状态码: {submitter_result.get('code')}")
            custom_print(f"  消息: {submitter_result.get('message')}")

            # 输出每批次的详细信息
            batch_results = submitter_result.get("details", [])
            for i, batch_result in enumerate(batch_results):
                custom_print(f"  批次 {i+1}:")
                custom_print(f"    状态码: {batch_result.get('code')}")
                custom_print(f"    消息: {batch_result.get('message')}")
                custom_print(f"    链接: {batch_result.get('links')}")

                # 输出响应详情
                response = batch_result.get("response")
                if response:
                    custom_print(f"    响应: {json.dumps(response, ensure_ascii=False)}")

        custom_print("=" * 50)
        custom_print("调试完成")
        custom_print("=" * 50)
    finally:
        # 关闭资源提交管理器
        await resource_submit_manager.close()


if __name__ == "__main__":
    # 如果有命令行参数，使用第一个参数作为测试URL
    test_url = sys.argv[1] if len(sys.argv) > 1 else None
    asyncio.run(debug_submit_resource(test_url))
