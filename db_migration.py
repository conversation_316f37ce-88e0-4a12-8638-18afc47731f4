"""
数据库迁移脚本
用于创建新表和迁移已有数据
"""
import os
import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from utils import custom_print, get_datetime


def ensure_db_dir(db_path: str) -> None:
    """确保数据库目录存在"""
    db_dir = os.path.dirname(db_path)
    os.makedirs(db_dir, exist_ok=True)


def create_submit_records_table(db_path: str) -> bool:
    """
    创建资源提交记录表
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 是否成功创建表
    """
    try:
        # 确保数据库目录存在
        ensure_db_dir(db_path)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查提交记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            # 创建提交记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS submit_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                share_url TEXT NOT NULL,
                submitter_type TEXT NOT NULL,
                submit_time TEXT NOT NULL,
                status INTEGER DEFAULT 1,
                response TEXT,
                UNIQUE(share_url, submitter_type)
            )
            ''')
            custom_print("创建资源提交记录表成功")
        else:
            custom_print("资源提交记录表已存在")
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        custom_print(f"创建资源提交记录表失败: {e}", error_msg=True)
        return False


def migrate_share_records_to_submit_records(db_path: str, submitter_type: str = "krzb") -> bool:
    """
    将share_records表中的已提交记录迁移到submit_records表
    
    Args:
        db_path: 数据库文件路径
        submitter_type: 提交器类型
        
    Returns:
        bool: 是否成功迁移数据
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查share_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            custom_print("分享记录表不存在，无法迁移数据", error_msg=True)
            conn.close()
            return False
        
        # 检查share_records表是否有submitted字段
        cursor.execute("PRAGMA table_info(share_records)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if "submitted" not in column_names:
            custom_print("分享记录表没有submitted字段，无法迁移数据", error_msg=True)
            conn.close()
            return False
        
        # 查询已提交的分享记录
        cursor.execute(
            """SELECT id, share_url, submit_time, submit_result FROM share_records 
               WHERE status = 1 AND share_url != '' AND submitted = 1"""
        )
        records = cursor.fetchall()
        
        if not records:
            custom_print("没有已提交的分享记录，无需迁移数据")
            conn.close()
            return True
        
        custom_print(f"找到 {len(records)} 条已提交的分享记录")
        
        # 迁移数据到submit_records表
        current_time = get_datetime(fmt="%Y-%m-%d %H:%M:%S")
        migrated_count = 0
        
        for record in records:
            record_id, share_url, submit_time, submit_result = record
            
            # 如果submit_time为空，使用当前时间
            if not submit_time:
                submit_time = current_time
            
            try:
                # 尝试插入新记录
                cursor.execute(
                    """INSERT OR REPLACE INTO submit_records
                       (share_url, submitter_type, submit_time, status, response)
                       VALUES (?, ?, ?, ?, ?)""",
                    (share_url, submitter_type, submit_time, 1, submit_result or "")
                )
                migrated_count += 1
            except Exception as e:
                custom_print(f"迁移记录 {record_id} 失败: {e}", error_msg=True)
        
        conn.commit()
        conn.close()
        
        custom_print(f"成功迁移 {migrated_count}/{len(records)} 条记录到submit_records表")
        return True
    except Exception as e:
        custom_print(f"迁移数据失败: {e}", error_msg=True)
        return False


def add_submitted_field_to_share_records(db_path: str) -> bool:
    """
    确保share_records表中存在submitted字段
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 是否成功添加字段
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查share_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            custom_print("分享记录表不存在，无法添加字段", error_msg=True)
            conn.close()
            return False
        
        # 检查是否存在submitted字段
        cursor.execute("PRAGMA table_info(share_records)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果不存在submitted字段，添加该字段
        fields_added = False
        if "submitted" not in column_names:
            try:
                cursor.execute("ALTER TABLE share_records ADD COLUMN submitted INTEGER DEFAULT 0")
                fields_added = True
            except sqlite3.OperationalError as e:
                custom_print(f"添加submitted字段失败: {e}", error_msg=True)
                conn.close()
                return False
        
        if "submit_time" not in column_names:
            try:
                cursor.execute("ALTER TABLE share_records ADD COLUMN submit_time TEXT")
                fields_added = True
            except sqlite3.OperationalError as e:
                custom_print(f"添加submit_time字段失败: {e}", error_msg=True)
                conn.close()
                return False
        
        if "submit_result" not in column_names:
            try:
                cursor.execute("ALTER TABLE share_records ADD COLUMN submit_result TEXT")
                fields_added = True
            except sqlite3.OperationalError as e:
                custom_print(f"添加submit_result字段失败: {e}", error_msg=True)
                conn.close()
                return False
        
        conn.commit()
        conn.close()
        
        if fields_added:
            custom_print("已添加资源提交相关字段到分享记录表")
        else:
            custom_print("分享记录表已包含所有必要字段")
        
        return True
    except Exception as e:
        custom_print(f"添加字段失败: {e}", error_msg=True)
        return False


def run_migration():
    """运行数据库迁移"""
    db_path = 'data/quark_links.db'
    
    custom_print("=" * 50)
    custom_print("开始数据库迁移")
    custom_print("=" * 50)
    
    # 确保share_records表中存在submitted字段
    if not add_submitted_field_to_share_records(db_path):
        custom_print("添加submitted字段失败，迁移终止", error_msg=True)
        return False
    
    # 创建submit_records表
    if not create_submit_records_table(db_path):
        custom_print("创建submit_records表失败，迁移终止", error_msg=True)
        return False
    
    # 迁移数据
    if not migrate_share_records_to_submit_records(db_path):
        custom_print("迁移数据失败", error_msg=True)
        return False
    
    custom_print("=" * 50)
    custom_print("数据库迁移完成")
    custom_print("=" * 50)
    return True


if __name__ == "__main__":
    run_migration()
