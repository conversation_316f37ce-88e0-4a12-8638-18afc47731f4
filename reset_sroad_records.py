#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重置Sroad提交记录
"""

import sqlite3
from utils import custom_print


def run_reset() -> bool:
    """
    重置Sroad提交记录，将所有记录标记为未提交
    
    Returns:
        bool: 是否成功重置
    """
    try:
        # 连接数据库
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()
        
        # 检查submit_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            custom_print("提交记录表不存在，无需重置", error_msg=True)
            conn.close()
            return False
        
        # 删除所有Sroad的提交记录
        cursor.execute("DELETE FROM submit_records WHERE submitter_type = 'sroad'")
        conn.commit()
        
        deleted_count = cursor.rowcount
        custom_print(f"已重置 {deleted_count} 条Sroad提交记录")
        
        conn.close()
        return True
    except Exception as e:
        custom_print(f"重置Sroad提交记录失败: {e}", error_msg=True)
        return False


if __name__ == "__main__":
    # 询问用户是否确认重置
    confirm = input("确定要重置Sroad提交记录吗？这将删除所有Sroad提交记录(y/n): ")
    if confirm.lower() == 'y':
        if run_reset():
            custom_print("Sroad提交记录重置成功")
        else:
            custom_print("Sroad提交记录重置失败", error_msg=True)
    else:
        custom_print("已取消重置操作")
