# QuarkPanTool

[![Python Version](https://img.shields.io/badge/python-3.11.6-blue.svg)](https://www.python.org/downloads/release/python-3116/)
[![Latest Release](https://img.shields.io/github/v/release/ihmily/QuarkPanTool)](https://github.com/ihmily/QuarkPanTool/releases/latest)
[![Downloads](https://img.shields.io/github/downloads/ihmily/QuarkPanTool/total)](https://github.com/ihmily/QuarkPanTool/releases/latest)
![GitHub Repo stars](https://img.shields.io/github/stars/ihmily/QuarkPanTool?style=social)


QuarkPanTool 是一个功能强大的网盘资源管理工具，旨在帮助用户快速批量转存分享文件、批量生成分享链接、自动获取RSS资源、提交分享资源到资源站点，以及提供详细的数据统计分析。

## 功能特点

- 运行稳定：基于playwright支持网页登录网盘，无需手动获取Cookie。
- 轻松操作：简洁直观的命令行界面，方便快捷地完成文件转存。
- 批量转存：支持一次性转存多个网盘分享链接中的文件。
- 批量分享：支持一次性将某个文件夹内的所有文件夹批量生成分享链接，无需手动分享文件。
- 本地下载：支持批量下载网盘文件夹中的所有文件。
- 自动运行：支持自动从RSS源获取网盘链接并转存到指定文件夹。
- 日期文件夹：自动运行时会在网盘根目录下创建以当前日期命名的文件夹。
- 去重功能：使用SQLite数据库记录已转存的链接，避免重复保存。
- 资源提交：支持将分享链接自动提交到多个资源站点。
- 数据统计：提供分享浏览统计和订阅源统计功能，支持导出Excel报表。
- 非RSS格式支持：能够处理非RSS/XML格式的订阅源，自动提取网页中的分享链接。

## 如何使用

如果不想自己部署环境，可以下载打包好的可执行文件(exe)压缩包 [QuarkPanTool](https://github.com/ihmily/QuarkPanTool/releases) ，解压后直接运行即可。

1.下载代码

```
git clone https://github.com/ihmily/QuarkPanTool.git
```

2.安装依赖

```
pip install -r requirements.txt
playwright install firefox
```

3.运行

```
python quark.py
```

运行后会使用playwright进行登录操作，当然也可以自己手动获取cookie填写到config/cookies.txt文件中。

## 功能详解

### 1. 分享地址转存文件

将分享链接中的文件转存到您的网盘中。支持单个链接转存和批量转存。批量转存时，请先在url.txt文件中填写网盘分享地址（一行一个）。

### 2. 批量生成分享链接

选择一个文件夹，自动为其中的所有子文件夹生成分享链接，并保存到文件中。执行批量分享文件夹时，需要使用上两级文件夹内页面的地址，如要分享文件夹B下的所有文件夹：

```
/文件夹A/文件夹B/文件夹1
/文件夹A/文件夹B/文件夹2
```

此时需要输入的页面地址就是文件夹A内的页面地址。

### 3. 切换网盘保存目录

更改文件转存的目标目录。

### 4. 创建网盘文件夹

在当前目录下创建新的文件夹。

### 5. 下载到本地

将网盘中的文件下载到本地。支持单个文件下载和批量下载。

### 6. 登录

登录到您的网盘账号。首次运行会比较缓慢，程序会自动打开一个浏览器，让你登录网盘，登录完成后，请不要手动关闭浏览器，回到软件界面按Enter键，浏览器会自动关闭并保存你的登录信息，下次运行就不需要登录了。（如果是Linux环境，请自行在网页获取Cookie后填入config/cookies.txt文件使用）

### 7. 自动运行

自动从配置的RSS源获取分享链接，并转存到指定文件夹。支持单次运行和定时运行。

**自动运行流程**：
1. 从配置文件加载RSS源
2. 创建以当前日期命名的文件夹
3. 依次获取每个RSS源的内容
4. 从RSS内容中提取分享链接
5. 将链接转存到创建的文件夹中
6. 检查未转存的链接并尝试转存
7. 检查未分享的文件夹并进行分享
8. 如果启用了自动提交功能，将分享链接提交到配置的资源站点

### 8. 提交分享资源

将分享链接提交到配置的资源站点，支持多个站点同时提交。

### 9. 快速调试资源提交

用于测试资源提交功能是否正常工作。

### 10. 数据库迁移

更新数据库结构，添加新的字段或表。

### 11. 重置提交记录

重置特定站点的资源提交记录。

### 12. 分享浏览统计

统计分享链接的浏览量、保存次数、下载次数等数据，并生成详细报告。支持导出Excel格式的统计报表。

### 13. 订阅源统计

分析订阅源的数据，包括：
- 哪个订阅源分享的链接浏览量最高
- 按订阅源统计浏览量、保存次数、下载次数
- 显示每个订阅源的最受欢迎分享
- 统计未转存过链接的订阅源
- 显示未转存链接最多的订阅源及其链接列表
- 显示从未获取到分享链接的订阅源

### 14. 修复RSS源记录

修复RSS源记录中的问题。

## 注意事项

- 使用自动运行功能时，可以在config/rss_config.json文件中配置订阅源和其他参数：
  ```json
  {
      "sources": [
          {
              "url": "rsshub://telegram/channel/XiangxiuNB",
              "type": "rss"
          },
          {
              "url": "rsshub://telegram/channel/yunpanpan",
              "type": "rss"
          },
          {
              "url": "https://linux.do/tag/%E5%BD%B1%E8%A7%86.rss",
              "type": "rss"
          },
          {
              "url": "https://example.com/movies",
              "type": "web"
          }
      ],
      "rss_sources": [
          "rsshub://telegram/channel/XiangxiuNB",
          "rsshub://telegram/channel/yunpanpan"
      ],
      "auto_run_interval": 3600,
      "quark_link_patterns": [
          "https://pan.quark.cn/s/[0-9a-f]{12,16}",
          "https://pan.quark.cn/s/[0-9a-zA-Z]{12,16}"
      ],
      "proxy": {
          "enabled": false,
          "http": "http://127.0.0.1:7890",
          "https": "http://127.0.0.1:7890",
          "socks5": "socks5://127.0.0.1:7890"
      },
      "fetch_article_content": true,
      "auto_share": true,
      "share_settings": {
          "url_type": 1,
          "expired_type": 4,
          "password": "",
          "delay": {
              "initial": [3.0, 5.0],
              "before_each": [1.0, 3.0],
              "after_each": [2.0, 5.0]
          }
      },
      "resource_submit": {
          "enabled": true,
          "auto_submit": true,
          "submitters": ["site1", "site2", "site3"],
          "submit_delay": {
              "min": 1,
              "max": 5
          },
          "check_limit": 50,
          "avoid_duplicate": true,
          "site1": {
              "enabled": true,
              "api_url": "https://api.example1.com",
              "batch_size": 4,
              "use_proxy": false
          },
          "site2": {
              "enabled": true,
              "api_url": "https://api.example2.com",
              "batch_size": 1,
              "use_proxy": false
          },
          "site3": {
              "enabled": true,
              "api_url": "https://api.example3.com",
              "batch_size": 1,
              "use_proxy": false
          }
      }
  }
  ```

### 配置项说明

- **sources**: 订阅源列表，支持不同类型的订阅源
  - `url`: 订阅源URL
  - `type`: 订阅源类型，支持 `rss` 和 `web`
    - `rss`: RSS/XML格式的订阅源，支持rsshub://格式
    - `web`: 普通网页，程序会自动提取网页中的网盘链接
- **rss_sources**: 旧版本的RSS源列表，为了兼容性保留
- **auto_run_interval**: 自动运行的间隔时间（秒）
- **quark_link_patterns**: 网盘链接的正则表达式模式
- **proxy**: 代理配置
  - `enabled`: 是否启用代理（true/false）
  - `http`: HTTP代理地址
  - `https`: HTTPS代理地址
  - `socks5`: SOCKS5代理地址
- **fetch_article_content**: 是否获取文章详细内容（true/false）
- **auto_share**: 是否自动分享转存的文件夹（true/false）
- **share_settings**: 分享设置
  - `url_type`: 分享链接类型(1=公开，2=私密)
  - `expired_type`: 过期类型(1=1天，2=7天，3=30天，4=永久)
  - `password`: 提取码(仅在url_type=2时有效)
  - `delay`: 分享操作的延迟设置
    - `initial`: 开始分享前的初始延迟范围[最小值, 最大值]
    - `before_each`: 每次分享前的延迟范围
    - `after_each`: 每次分享后的延迟范围
- **resource_submit**: 资源提交设置
  - `enabled`: 是否启用资源提交功能（true/false）
  - `auto_submit`: 是否自动提交（true/false）
  - `submitters`: 启用的提交器列表
  - `submit_delay`: 提交延迟设置
    - `min`: 最小延迟时间（秒）
    - `max`: 最大延迟时间（秒）
  - `check_limit`: 检查未提交记录的数量限制
  - `avoid_duplicate`: 是否避免重复提交（true/false）
  - 各站点配置: 每个站点可以单独配置
    - `enabled`: 是否启用该站点（true/false）
    - `api_url`: 站点API地址
    - `batch_size`: 批量提交大小
    - `use_proxy`: 是否使用代理（true/false）

注意：获取RSS源内容时默认使用代理，其他操作（如转存链接、下载文件等）仅在网络请求失败或超时时才会自动切换到使用代理。

## 效果演示

![ScreenShot1](./images/Snipaste_2024-09-23_19-02-03.jpg)

## 数据库结构

程序使用SQLite数据库存储数据，位于`data/quark_links.db`：

### 表结构

1. **saved_links**: 存储已转存的链接
   - id: 自增主键
   - url: 链接URL(唯一)
   - title: 链接标题
   - save_date: 保存日期
   - folder_id: 保存的文件夹ID
   - folder_name: 保存的文件夹名称
   - status: 状态(0=失败，1=成功)
   - transfer_status: 转存状态(0=未转存，1=已成功转存，2=转存失败)
   - transfer_date: 转存日期
   - retry_count: 重试次数
   - last_error: 最后一次错误信息

2. **share_records**: 存储分享记录
   - id: 自增主键
   - folder_id: 父文件夹ID
   - folder_name: 父文件夹名称
   - subfolder_id: 子文件夹ID
   - subfolder_name: 子文件夹名称
   - share_url: 分享链接
   - share_time: 分享时间
   - status: 状态(0=失败，1=成功)
   - error_message: 错误信息
   - submitted: 是否已提交(0=未提交，1=已提交)
   - submit_time: 提交时间
   - submit_result: 提交结果

3. **rss_links**: 存储从RSS源获取的链接
   - id: 自增主键
   - source_name: 订阅源名称
   - source_url: 订阅源URL
   - share_link: 分享链接
   - is_saved: 是否已保存(0=未保存，1=已保存)
   - fetch_time: 获取时间

4. **submit_records**: 存储资源提交记录
   - id: 自增主键
   - share_url: 分享链接
   - submitter_type: 提交器类型
   - submit_time: 提交时间
   - status: 状态(0=失败，1=成功)
   - response: 响应内容

## 许可证

QuarkPanTool 使用 [Apache-2.0 license](https://github.com/ihmily/QuarkPanTool#Apache-2.0-1-ov-file) 许可证，详情请参阅 LICENSE 文件。

------

**免责声明**：本工具仅供学习和研究使用，请勿用于非法目的。由使用本工具引起的任何法律责任，与本工具作者无关。
