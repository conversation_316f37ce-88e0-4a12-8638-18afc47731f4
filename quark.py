# -*- coding: utf-8 -*-

import asyncio
import re
import sys
import httpx
import sqlite3
import feedparser
from datetime import datetime
from prettytable import PrettyTable
from tqdm import tqdm
from quark_login import QuarkLogin, CONFIG_DIR
from utils import *
import json
import os
import random
from typing import List, Dict, Union, Tuple, Any, Optional
from playwright.async_api import async_playwright
import time
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils import get_column_letter

# 导入资源提交模块
from resource_submitters.resource_submit_manager import ResourceSubmitManager
import quark_resource_submit
import debug_resource_submit
import db_migration
import reset_yunso_records


class QuarkPanFileManager:
    def __init__(self, headless: bool = False, slow_mo: int = 0) -> None:
        self.headless: bool = headless
        self.slow_mo: int = slow_mo
        self.folder_id: Union[str, None] = None
        self.user: Union[str, None] = '用户A'
        self.pdir_id: Union[str, None] = '0'
        self.dir_name: Union[str, None] = '根目录'

        # 加载RSS配置
        self.rss_config = self.load_rss_config()

        # 获取cookies
        try:
            self.cookies: str = self.get_cookies()
            if not self.cookies:
                custom_print("获取cookies失败，请重新登录", error_msg=True)
                self.clear_cookies_and_exit()
        except Exception as e:
            custom_print(f"获取cookies失败: {e}", error_msg=True)
            self.clear_cookies_and_exit()

        # 设置请求头
        self.headers: Dict[str, str] = {
            'user-agent': self.get_random_ua(),
            'origin': 'https://pan.quark.cn',
            'referer': 'https://pan.quark.cn/',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cookie': self.cookies,
        }

        # 初始化数据库
        self.init_database()

        # 运行数据库迁移
        try:
            db_migration.run_migration()
        except Exception as e:
            custom_print(f"数据库迁移失败: {e}", error_msg=True)

        # 不再在程序启动时自动重置云搜网提交记录
        # 如需重置，请使用选项11

        # 初始化资源提交管理器
        self.resource_submit_manager = None
        if self.rss_config and "resource_submit" in self.rss_config:
            # 确保submitters列表包含yiso、buyutu、sroad和macyeah
            resource_submit_config = self.rss_config.get("resource_submit", {})
            if "submitters" in resource_submit_config:
                if "yiso" not in resource_submit_config["submitters"]:
                    resource_submit_config["submitters"].append("yiso")
                    custom_print("已添加yiso提交器到配置")
                if "buyutu" not in resource_submit_config["submitters"]:
                    resource_submit_config["submitters"].append("buyutu")
                    custom_print("已添加步游兔提交器到配置")
                if "sroad" not in resource_submit_config["submitters"]:
                    resource_submit_config["submitters"].append("sroad")
                    custom_print("已添加Sroad提交器到配置")
                if "macyeah" not in resource_submit_config["submitters"]:
                    resource_submit_config["submitters"].append("macyeah")
                    custom_print("已添加马克耶网盘提交器到配置")

            # 确保yiso配置存在
            if "yiso" not in resource_submit_config:
                resource_submit_config["yiso"] = {
                    "enabled": True,
                    "api_url": "https://yiso.fun/api/member/share",
                    "batch_size": 1,
                    "use_proxy": False
                }
                custom_print("已添加yiso默认配置")

            # 确保buyutu配置存在
            if "buyutu" not in resource_submit_config:
                resource_submit_config["buyutu"] = {
                    "enabled": True,
                    "api_url": "https://www.buyutu.com/sub",
                    "batch_size": 10,
                    "use_proxy": False,
                    "retry_count": 3,
                    "delay": {
                        "min": 1.0,
                        "max": 3.0
                    },
                    "random_ua": True
                }
                custom_print("已添加步游兔默认配置")

            # 确保sroad配置存在
            if "sroad" not in resource_submit_config:
                resource_submit_config["sroad"] = {
                    "enabled": True,
                    "api_url": "https://hk10g.sroad.win/sub",
                    "batch_size": 10,
                    "use_proxy": False,
                    "retry_count": 3,
                    "delay": {
                        "min": 1.0,
                        "max": 3.0
                    },
                    "random_ua": True
                }
                custom_print("已添加Sroad默认配置")

            self.resource_submit_manager = ResourceSubmitManager(resource_submit_config)

    def get_random_ua(self) -> str:
        """获取随机User-Agent"""
        ua_list = [
            # Chrome
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            # Firefox
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (X11; Linux i686; rv:109.0) Gecko/20100101 Firefox/119.0',
            # Safari
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            # Edge
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            # Opera
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********',
        ]
        return random.choice(ua_list)

    def get_cookies(self) -> str:
        """获取cookies，如果失败则返回空字符串"""
        try:
            quark_login = QuarkLogin(headless=self.headless, slow_mo=self.slow_mo)
            cookies: str = quark_login.get_cookies()

            # 验证cookies是否有效
            if not cookies or len(cookies.strip()) == 0:
                custom_print("获取到的cookies为空", error_msg=True)
                return ""

            return cookies
        except Exception as e:
            custom_print(f"获取cookies异常: {e}", error_msg=True)
            return ""

    @staticmethod
    def get_pwd_id(share_url: str) -> str:
        return share_url.split('?')[0].split('/s/')[1]

    @staticmethod
    def extract_urls(text: str) -> list:
        url_pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
        urls = re.findall(url_pattern, text)
        return urls[0] if urls else ""

    def init_database(self) -> None:
        """初始化SQLite数据库，用于存储已转存的网盘链接和分享记录"""
        os.makedirs('data', exist_ok=True)
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 检查转存链接表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='saved_links'")
        table_exists = cursor.fetchone()

        if not table_exists:
            # 创建新表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS saved_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE,
                title TEXT,
                save_date TEXT,
                folder_id TEXT,
                folder_name TEXT,
                status INTEGER DEFAULT 0,
                transfer_status INTEGER DEFAULT 0,
                transfer_date TEXT,
                retry_count INTEGER DEFAULT 0,
                last_error TEXT
            )
            ''')
        else:
            # 检查是否需要添加新列
            try:
                # 检查transfer_status列是否存在
                cursor.execute("SELECT transfer_status FROM saved_links LIMIT 1")
            except sqlite3.OperationalError:
                # 列不存在，添加新列
                cursor.execute("ALTER TABLE saved_links ADD COLUMN transfer_status INTEGER DEFAULT 0")
                cursor.execute("ALTER TABLE saved_links ADD COLUMN transfer_date TEXT")
                cursor.execute("ALTER TABLE saved_links ADD COLUMN retry_count INTEGER DEFAULT 0")
                cursor.execute("ALTER TABLE saved_links ADD COLUMN last_error TEXT")
                custom_print("数据库结构已更新，添加了转存状态跟踪功能")

        # 检查分享记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        share_table_exists = cursor.fetchone()

        if not share_table_exists:
            # 创建分享记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS share_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                folder_id TEXT NOT NULL,
                folder_name TEXT NOT NULL,
                subfolder_id TEXT NOT NULL,
                subfolder_name TEXT NOT NULL,
                share_url TEXT NOT NULL,
                share_time TEXT NOT NULL,
                status INTEGER DEFAULT 1,
                error_message TEXT
            )
            ''')
            custom_print("创建分享记录表成功")

        # 检查RSS链接记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='rss_links'")
        rss_links_table_exists = cursor.fetchone()

        if not rss_links_table_exists:
            # 创建RSS链接记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS rss_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT NOT NULL,
                source_url TEXT NOT NULL,
                share_link TEXT NOT NULL,
                is_saved INTEGER DEFAULT 0,
                fetch_time TEXT NOT NULL,
                UNIQUE(source_url, share_link)
            )
            ''')
            custom_print("创建RSS链接记录表成功")

        conn.commit()
        conn.close()

    def load_rss_config(self) -> Dict:
        """加载RSS配置文件"""
        try:
            os.makedirs(CONFIG_DIR, exist_ok=True)
            config_path = f'{CONFIG_DIR}/rss_config.json'

            default_config = {
                "sources": [
                    {
                        "url": "rsshub://telegram/channel/XiangxiuNB",
                        "type": "rss"
                    },
                    {
                        "url": "rsshub://telegram/channel/yunpanpan",
                        "type": "rss"
                    }
                ],
                "rss_sources": [
                    "rsshub://telegram/channel/XiangxiuNB",
                    "rsshub://telegram/channel/yunpanpan"
                ],
                "auto_run_interval": 3600,
                "quark_link_patterns": [
                    "https://pan.quark.cn/s/[0-9a-f]{12,16}",
                    "https://pan.quark.cn/s/[0-9a-zA-Z]{12,16}"
                ],
                "proxy": {
                    "enabled": False,
                    "http": "http://127.0.0.1:7890",
                    "https": "http://127.0.0.1:7890",
                    "socks5": "socks5://127.0.0.1:7890"
                },
                "fetch_article_content": True,
                "auto_share": False,
                "share_settings": {
                    "url_type": 1,  # 1=公开分享，2=私密分享
                    "expired_type": 4,  # 1=1天，2=7天，3=30天，4=永久
                    "password": "",  # 提取码，仅在url_type=2时有效
                    "delay": {
                        "initial": [3.0, 5.0],  # 开始分享前的初始延迟范围[最小值, 最大值]
                        "before_each": [1.0, 3.0],  # 每次分享前的延迟范围
                        "after_each": [2.0, 5.0]  # 每次分享后的延迟范围
                    }
                },
                "resource_submit": {
                    "enabled": True,
                    "auto_submit": True,
                    "submitters": ["krzb", "yiso", "buyutu"],
                    "submit_delay": {
                        "min": 1,
                        "max": 5
                    },
                    "check_limit": 50,
                    "avoid_duplicate": True,
                    "krzb": {
                        "enabled": True,
                        "api_url": "https://fc-resource-node-api.krzb.net",
                        "batch_size": 4,
                        "use_proxy": False
                    },
                    "yiso": {
                        "enabled": True,
                        "api_url": "https://yiso.fun/api/member/share",
                        "batch_size": 1,
                        "use_proxy": False
                    },
                    "buyutu": {
                        "enabled": True,
                        "api_url": "https://www.buyutu.com/sub",
                        "batch_size": 10,
                        "use_proxy": False,
                        "retry_count": 3,
                        "delay": {
                            "min": 1.0,
                            "max": 3.0
                        },
                        "random_ua": True
                    }
                }
            }

            # 检查配置文件是否存在
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    # 验证配置是否完整
                    if not isinstance(config, dict):
                        custom_print(f"RSS配置格式错误，使用默认配置", error_msg=True)
                        return default_config

                    # 确保所有必要的字段都存在
                    for key in default_config:
                        if key not in config:
                            custom_print(f"RSS配置缺少 {key} 字段，使用默认值", error_msg=True)
                            config[key] = default_config[key]

                    # 确保proxy字段存在且完整
                    if "proxy" not in config or not isinstance(config["proxy"], dict):
                        custom_print(f"RSS配置缺少 proxy 字段或格式错误，使用默认值", error_msg=True)
                        config["proxy"] = default_config["proxy"]
                    else:
                        for key in default_config["proxy"]:
                            if key not in config["proxy"]:
                                custom_print(f"代理配置缺少 {key} 字段，使用默认值", error_msg=True)
                                config["proxy"][key] = default_config["proxy"][key]

                    # 确保auto_share字段存在
                    if "auto_share" not in config:
                        custom_print(f"RSS配置缺少 auto_share 字段，使用默认值", error_msg=True)
                        config["auto_share"] = default_config["auto_share"]

                    # 确保share_settings字段存在且完整
                    if "share_settings" not in config or not isinstance(config["share_settings"], dict):
                        custom_print(f"RSS配置缺少 share_settings 字段或格式错误，使用默认值", error_msg=True)
                        config["share_settings"] = default_config["share_settings"]
                    else:
                        for key in default_config["share_settings"]:
                            if key not in config["share_settings"]:
                                custom_print(f"分享设置缺少 {key} 字段，使用默认值", error_msg=True)
                                config["share_settings"][key] = default_config["share_settings"][key]

                    # 确保resource_submit字段存在且完整
                    if "resource_submit" not in config or not isinstance(config["resource_submit"], dict):
                        custom_print(f"RSS配置缺少 resource_submit 字段或格式错误，使用默认值", error_msg=True)
                        config["resource_submit"] = default_config["resource_submit"]
                    else:
                        for key in default_config["resource_submit"]:
                            if key not in config["resource_submit"]:
                                custom_print(f"资源提交设置缺少 {key} 字段，使用默认值", error_msg=True)
                                config["resource_submit"][key] = default_config["resource_submit"][key]

                    return config
                except json.JSONDecodeError:
                    custom_print(f"RSS配置文件格式错误，使用默认配置", error_msg=True)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(default_config, f, ensure_ascii=False, indent=4)
                    return default_config
            else:
                custom_print(f"RSS配置文件不存在，创建默认配置", error_msg=True)
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=4)
                return default_config
        except Exception as e:
            custom_print(f"加载RSS配置失败: {e}，使用默认配置", error_msg=True)
            return {
                "sources": [
                    {
                        "url": "rsshub://telegram/channel/XiangxiuNB",
                        "type": "rss"
                    },
                    {
                        "url": "rsshub://telegram/channel/yunpanpan",
                        "type": "rss"
                    }
                ],
                "rss_sources": [
                    "rsshub://telegram/channel/XiangxiuNB",
                    "rsshub://telegram/channel/yunpanpan"
                ],
                "auto_run_interval": 3600,
                "quark_link_patterns": [
                    "https://pan.quark.cn/s/[0-9a-f]{12,16}",
                    "https://pan.quark.cn/s/[0-9a-zA-Z]{12,16}"
                ],
                "proxy": {
                    "enabled": False,
                    "http": "http://127.0.0.1:7890",
                    "https": "http://127.0.0.1:7890",
                    "socks5": "socks5://127.0.0.1:7890"
                },
                "fetch_article_content": True,
                "auto_share": False,
                "share_settings": {
                    "url_type": 1,  # 1=公开分享，2=私密分享
                    "expired_type": 4,  # 1=1天，2=7天，3=30天，4=永久
                    "password": "",  # 提取码，仅在url_type=2时有效
                    "delay": {
                        "initial": [3.0, 5.0],  # 开始分享前的初始延迟范围[最小值, 最大值]
                        "before_each": [1.0, 3.0],  # 每次分享前的延迟范围
                        "after_each": [2.0, 5.0]  # 每次分享后的延迟范围
                    }
                },
                "resource_submit": {
                    "enabled": True,
                    "auto_submit": True,
                    "submitters": ["krzb", "yiso", "buyutu", "sroad"],
                    "submit_delay": {
                        "min": 1,
                        "max": 5
                    },
                    "check_limit": 50,
                    "avoid_duplicate": True,
                    "krzb": {
                        "enabled": True,
                        "api_url": "https://fc-resource-node-api.krzb.net",
                        "batch_size": 4,
                        "use_proxy": False
                    },
                    "yiso": {
                        "enabled": True,
                        "api_url": "https://yiso.fun/api/member/share",
                        "batch_size": 1,
                        "use_proxy": False
                    },
                    "buyutu": {
                        "enabled": True,
                        "api_url": "https://www.buyutu.com/sub",
                        "batch_size": 10,
                        "use_proxy": False,
                        "retry_count": 3,
                        "delay": {
                            "min": 1.0,
                            "max": 3.0
                        },
                        "random_ua": True
                    },
                    "sroad": {
                        "enabled": True,
                        "api_url": "https://hk10g.sroad.win/sub",
                        "batch_size": 10,
                        "use_proxy": False,
                        "retry_count": 3,
                        "delay": {
                            "min": 1.0,
                            "max": 3.0
                        },
                        "random_ua": True
                    }
                }
            }

    def extract_quark_links(self, text: str) -> List[str]:
        """从文本中提取夸克网盘链接"""
        if not text:
            return []

        links = []
        try:
            # 确保rss_config存在
            if not hasattr(self, 'rss_config') or self.rss_config is None:
                custom_print("RSS配置未加载，尝试重新加载", error_msg=True)
                self.rss_config = self.load_rss_config()

            # 获取链接模式
            patterns = self.rss_config.get("quark_link_patterns", []) if self.rss_config else []

            # 如果没有配置模式，使用默认模式
            if not patterns:
                patterns = [
                    "https://pan.quark.cn/s/[0-9a-f]{12,16}",
                    "https://pan.quark.cn/s/[0-9a-zA-Z]{12,16}"
                ]

            # 提取链接
            for pattern in patterns:
                try:
                    found_links = re.findall(pattern, text)
                    links.extend(found_links)
                except re.error as e:
                    custom_print(f"正则表达式错误: {pattern} - {e}", error_msg=True)

            # 去重
            links = list(set(links))
            return links
        except Exception as e:
            custom_print(f"提取链接失败: {e}", error_msg=True)
            # 使用简单的URL提取作为备选
            try:
                url_pattern = r'https://pan\.quark\.cn/s/[0-9a-zA-Z]{12,16}'
                return re.findall(url_pattern, text)
            except Exception:
                return []

    def is_link_saved(self, url: str, check_transfer: bool = False) -> bool:
        """
        检查链接是否已经保存过

        Args:
            url: 要检查的链接
            check_transfer: 是否检查转存状态，如果为True，只有成功转存的链接才会返回True
        """
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        if check_transfer:
            # 检查链接是否存在且已成功转存
            cursor.execute("SELECT id FROM saved_links WHERE url = ? AND transfer_status = 1", (url,))
        else:
            # 只检查链接是否存在
            cursor.execute("SELECT id FROM saved_links WHERE url = ?", (url,))

        result = cursor.fetchone()
        conn.close()
        return result is not None

    def get_link_status(self, url: str) -> Dict[str, Any]:
        """获取链接的详细状态"""
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, url, title, save_date, folder_id, folder_name,
                   status, transfer_status, transfer_date, retry_count, last_error
            FROM saved_links WHERE url = ?
        """, (url,))
        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                "id": result[0],
                "url": result[1],
                "title": result[2],
                "save_date": result[3],
                "folder_id": result[4],
                "folder_name": result[5],
                "status": result[6],
                "transfer_status": result[7],
                "transfer_date": result[8],
                "retry_count": result[9],
                "last_error": result[10]
            }
        else:
            return {}

    def save_link_to_db(self, url: str, title: str, folder_id: str, folder_name: str,
                      transfer_status: int = 0, error_msg: str = "") -> None:
        """
        保存链接到数据库

        Args:
            url: 链接URL
            title: 链接标题
            folder_id: 保存的文件夹ID
            folder_name: 保存的文件夹名称
            transfer_status: 转存状态，0=未转存，1=已成功转存，2=转存失败
            error_msg: 如果转存失败，记录错误信息
        """
        current_time = get_datetime(fmt="%Y-%m-%d %H:%M:%S")

        # 检查链接是否已存在
        link_status = self.get_link_status(url)

        if link_status:
            # 链接已存在，更新状态
            conn = sqlite3.connect('data/quark_links.db')
            cursor = conn.cursor()

            # 如果是更新转存状态
            if transfer_status > 0:
                retry_count = link_status.get("retry_count", 0) + (1 if transfer_status == 2 else 0)
                cursor.execute(
                    """UPDATE saved_links SET
                       transfer_status = ?, transfer_date = ?, retry_count = ?, last_error = ?
                       WHERE url = ?""",
                    (transfer_status, current_time, retry_count, error_msg, url)
                )
                status_text = "成功" if transfer_status == 1 else "失败"
                custom_print(f"链接转存{status_text}: {url}")
            else:
                custom_print(f"链接已存在，跳过保存: {url}")

            conn.commit()
            conn.close()
            return

        # 链接不存在，新增记录
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 如果是新增带转存状态的记录
        if transfer_status > 0:
            transfer_date = current_time
            retry_count = 1 if transfer_status == 2 else 0
            cursor.execute(
                """INSERT INTO saved_links
                   (url, title, save_date, folder_id, folder_name, status,
                    transfer_status, transfer_date, retry_count, last_error)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (url, title, current_time, folder_id, folder_name, 1,
                 transfer_status, transfer_date, retry_count, error_msg)
            )
            status_text = "成功" if transfer_status == 1 else "失败"
            custom_print(f"链接已保存到数据库并转存{status_text}: {url}")
        else:
            # 只保存链接，不更新转存状态
            cursor.execute(
                """INSERT INTO saved_links
                   (url, title, save_date, folder_id, folder_name, status)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (url, title, current_time, folder_id, folder_name, 1)
            )
            custom_print(f"链接已保存到数据库: {url}")

        conn.commit()
        conn.close()

    def save_share_record(self, folder_id: str, folder_name: str, subfolder_id: str,
                         subfolder_name: str, share_url: str, status: int = 1,
                         error_message: str = "") -> None:
        """
        保存分享记录到数据库

        Args:
            folder_id: 父文件夹ID
            folder_name: 父文件夹名称
            subfolder_id: 子文件夹ID
            subfolder_name: 子文件夹名称
            share_url: 分享链接
            status: 状态，1=成功，0=失败
            error_message: 如果分享失败，记录错误信息
        """
        # 确保表存在
        self.ensure_share_records_table()

        current_time = get_datetime(fmt="%Y-%m-%d %H:%M:%S")

        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 检查是否已存在相同的记录
        cursor.execute(
            "SELECT id FROM share_records WHERE subfolder_id = ? AND share_url = ?",
            (subfolder_id, share_url)
        )
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            cursor.execute(
                """UPDATE share_records SET
                   folder_id = ?, folder_name = ?, subfolder_name = ?,
                   share_time = ?, status = ?, error_message = ?
                   WHERE subfolder_id = ? AND share_url = ?""",
                (folder_id, folder_name, subfolder_name, current_time,
                 status, error_message, subfolder_id, share_url)
            )
            custom_print(f"更新分享记录: {subfolder_name}")
        else:
            # 插入新记录
            cursor.execute(
                """INSERT INTO share_records
                   (folder_id, folder_name, subfolder_id, subfolder_name,
                    share_url, share_time, status, error_message)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (folder_id, folder_name, subfolder_id, subfolder_name,
                 share_url, current_time, status, error_message)
            )
            custom_print(f"保存分享记录: {subfolder_name}")

        conn.commit()
        conn.close()

    def ensure_share_records_table(self) -> None:
        """确保share_records表存在"""
        os.makedirs('data', exist_ok=True)
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 检查分享记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        share_table_exists = cursor.fetchone()

        if not share_table_exists:
            # 创建分享记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS share_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                folder_id TEXT NOT NULL,
                folder_name TEXT NOT NULL,
                subfolder_id TEXT NOT NULL,
                subfolder_name TEXT NOT NULL,
                share_url TEXT NOT NULL,
                share_time TEXT NOT NULL,
                status INTEGER DEFAULT 1,
                error_message TEXT
            )
            ''')
            custom_print("手动创建分享记录表成功")

        conn.commit()
        conn.close()

    def save_rss_link_record(self, source_name: str, source_url: str, share_link: str, is_saved: int = 0) -> None:
        """
        保存RSS链接记录到数据库

        Args:
            source_name: 订阅源名称
            source_url: 订阅源链接
            share_link: 分享链接
            is_saved: 是否已保存，0=未保存，1=已保存
        """
        current_time = get_datetime(fmt="%Y-%m-%d %H:%M:%S")

        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        try:
            # 检查是否已存在相同的记录
            cursor.execute(
                "SELECT id FROM rss_links WHERE source_url = ? AND share_link = ?",
                (source_url, share_link)
            )
            existing_record = cursor.fetchone()

            if existing_record:
                # 更新现有记录
                cursor.execute(
                    """UPDATE rss_links SET
                       source_name = ?, is_saved = ?, fetch_time = ?
                       WHERE source_url = ? AND share_link = ?""",
                    (source_name, is_saved, current_time, source_url, share_link)
                )
            else:
                # 插入新记录
                cursor.execute(
                    """INSERT INTO rss_links
                       (source_name, source_url, share_link, is_saved, fetch_time)
                       VALUES (?, ?, ?, ?, ?)""",
                    (source_name, source_url, share_link, is_saved, current_time)
                )

            conn.commit()
        except Exception as e:
            custom_print(f"保存RSS链接记录失败: {e}", error_msg=True)
        finally:
            conn.close()

    async def analyze_share_stats(self, record_count: int = 100) -> None:
        """
        分析分享浏览情况统计数据

        Args:
            record_count: 要查询的记录数量，默认为100

        功能：
        1. 计算总浏览量、总保存次数、总下载次数
        2. 找出最受欢迎的分享（浏览量最高）
        3. 计算转化率（如下载次数/浏览量）
        4. 按时间段分析浏览趋势
        5. 显示各分享的大小和所在文件夹
        """
        from datetime import datetime, timedelta
        from collections import defaultdict
        from prettytable import PrettyTable

        # 分享统计API URL
        url = "https://drive-pc.quark.cn/1/clouddrive/share/mypage/detail"

        # 查询参数
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "uc_param_str": "",
            "_page": "1",
            "_size": str(record_count),  # 用户指定的记录数量
            "_order_field": "created_at",
            "_order_type": "desc",
            "_fetch_total": "1",
            "_fetch_notify_follow": "1"
        }

        try:
            # 获取分享统计数据
            custom_print("正在获取分享统计数据...")
            async with await self.get_client() as client:
                response = await client.get(url, headers=self.headers, params=params)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"请求失败，状态码: {response.status_code}", error_msg=True)
                    return

                # 解析响应JSON
                try:
                    data = response.json()
                except Exception as e:
                    custom_print(f"解析响应JSON失败: {e}", error_msg=True)
                    return

                # 检查响应是否成功
                if data.get("status") != 200:
                    custom_print(f"API返回错误: {data.get('message', '未知错误')}", error_msg=True)
                    return

                # 提取分享记录
                if "data" not in data or "list" not in data["data"]:
                    custom_print("响应中没有找到分享记录列表", error_msg=True)
                    return

                all_shares = data["data"]["list"]
                total_count = data["metadata"].get("_total", 0)
                custom_print(f"找到 {len(all_shares)} 条分享记录，总共 {total_count} 条")

                # 过滤掉已删除的分享（status不等于1的记录）
                shares = [share for share in all_shares if share.get("status", 0) == 1]
                filtered_count = len(all_shares) - len(shares)

                if filtered_count > 0:
                    custom_print(f"已过滤 {filtered_count} 条已删除的分享记录")

                # 如果没有分享记录，直接返回
                if not shares:
                    custom_print("没有找到有效的分享记录", error_msg=True)
                    return

                # 获取数据库中的RSS链接记录，用于关联订阅源
                custom_print("正在从数据库获取RSS链接记录...")
                conn = sqlite3.connect('data/quark_links.db')
                cursor = conn.cursor()

                # 获取所有RSS链接记录
                cursor.execute("SELECT source_name, source_url, share_link FROM rss_links")
                rss_links_records = cursor.fetchall()
                conn.close()

                # 创建映射：夸克链接 -> RSS源信息
                quark_link_to_rss = {}
                if rss_links_records:
                    custom_print(f"从数据库中获取到 {len(rss_links_records)} 条RSS链接记录")
                    for record in rss_links_records:
                        source_name, source_url, share_link = record
                        if share_link not in quark_link_to_rss:
                            quark_link_to_rss[share_link] = []
                        quark_link_to_rss[share_link].append({
                            "source_name": source_name,
                            "source_url": source_url
                        })
                else:
                    custom_print("数据库中没有找到RSS链接记录，无法显示订阅源信息")

                # 1. 计算总浏览量、总保存次数、总下载次数
                total_views = sum(share.get("click_pv", 0) for share in shares)
                total_saves = sum(share.get("save_pv", 0) for share in shares)
                total_downloads = sum(share.get("download_pv", 0) for share in shares)

                custom_print("\n=== 总体统计 ===")
                custom_print(f"总浏览量: {total_views}")
                custom_print(f"总保存次数: {total_saves}")
                custom_print(f"总下载次数: {total_downloads}")

                # 计算平均值
                avg_views = total_views / len(shares) if shares else 0
                avg_saves = total_saves / len(shares) if shares else 0
                avg_downloads = total_downloads / len(shares) if shares else 0

                custom_print(f"平均浏览量: {avg_views:.2f}")
                custom_print(f"平均保存次数: {avg_saves:.2f}")
                custom_print(f"平均下载次数: {avg_downloads:.2f}")

                # 2. 找出最受欢迎的分享（浏览量最高）
                most_viewed = max(shares, key=lambda x: x.get("click_pv", 0))
                most_saved = max(shares, key=lambda x: x.get("save_pv", 0))
                most_downloaded = max(shares, key=lambda x: x.get("download_pv", 0))

                custom_print("\n=== 最受欢迎的分享 ===")
                custom_print(f"浏览量最高: {most_viewed.get('title')} | 浏览量: {most_viewed.get('click_pv')} | 链接: {most_viewed.get('share_url')}")
                custom_print(f"保存次数最多: {most_saved.get('title')} | 保存次数: {most_saved.get('save_pv')} | 链接: {most_saved.get('share_url')}")
                custom_print(f"下载次数最多: {most_downloaded.get('title')} | 下载次数: {most_downloaded.get('download_pv')} | 链接: {most_downloaded.get('share_url')}")

                # 3. 计算转化率
                custom_print("\n=== 转化率统计 ===")
                save_rate = total_saves / total_views * 100 if total_views > 0 else 0
                download_rate = total_downloads / total_views * 100 if total_views > 0 else 0
                custom_print(f"保存转化率: {save_rate:.2f}% (保存次数/浏览量)")
                custom_print(f"下载转化率: {download_rate:.2f}% (下载次数/浏览量)")

                # 4. 按时间段分析浏览趋势
                # 将分享记录按创建时间分组

                # 按天分组
                daily_views = defaultdict(int)
                daily_saves = defaultdict(int)
                daily_downloads = defaultdict(int)

                for share in shares:
                    # 将时间戳转换为日期
                    created_at = share.get("created_at", 0)
                    if created_at:
                        date = datetime.fromtimestamp(created_at / 1000).strftime("%Y-%m-%d")
                        daily_views[date] += share.get("click_pv", 0)
                        daily_saves[date] += share.get("save_pv", 0)
                        daily_downloads[date] += share.get("download_pv", 0)

                custom_print("\n=== 按日期分析浏览趋势 ===")
                # 按日期排序
                sorted_dates = sorted(daily_views.keys())
                for date in sorted_dates:
                    custom_print(f"日期: {date} | 浏览量: {daily_views[date]} | 保存次数: {daily_saves[date]} | 下载次数: {daily_downloads[date]}")

                # 5. 显示各分享的大小和所在文件夹
                custom_print("\n=== 分享详情 ===")
                custom_print("详细信息将导出到Excel表格中，终端仅显示总览信息")

                # 准备导出数据
                export_data = {
                    "overview": {
                        "总浏览量": total_views,
                        "总保存次数": total_saves,
                        "总下载次数": total_downloads,
                        "平均浏览量": f"{avg_views:.2f}",
                        "平均保存次数": f"{avg_saves:.2f}",
                        "平均下载次数": f"{avg_downloads:.2f}"
                    },
                    "rankings": {
                        "浏览量最高": f"{most_viewed.get('title')} | 浏览量: {most_viewed.get('click_pv')} | 链接: {most_viewed.get('share_url')}",
                        "保存次数最多": f"{most_saved.get('title')} | 保存次数: {most_saved.get('save_pv')} | 链接: {most_saved.get('share_url')}",
                        "下载次数最多": f"{most_downloaded.get('title')} | 下载次数: {most_downloaded.get('download_pv')} | 链接: {most_downloaded.get('share_url')}"
                    },
                    "conversion": {
                        "保存转化率": f"{save_rate:.2f}% (保存次数/浏览量)",
                        "下载转化率": f"{download_rate:.2f}% (下载次数/浏览量)"
                    },
                    "trends": [],
                    "details": []
                }

                # 添加趋势数据
                for date in sorted_dates:
                    export_data["trends"].append({
                        "date": date,
                        "views": daily_views[date],
                        "saves": daily_saves[date],
                        "downloads": daily_downloads[date]
                    })

                # 添加详情数据
                for share in shares:
                    title = share.get("title", "未知")
                    path_info = share.get("path_info", "未知")
                    size_mb = share.get("size", 0) / (1024 * 1024)  # 转换为MB
                    views = share.get("click_pv", 0)
                    saves = share.get("save_pv", 0)
                    downloads = share.get("download_pv", 0)
                    created_at = datetime.fromtimestamp(share.get("created_at", 0) / 1000).strftime("%Y-%m-%d %H:%M:%S")
                    share_url = share.get("share_url", "")

                    # 查找该分享链接对应的订阅源
                    source_name = ""
                    if share_url in quark_link_to_rss:
                        # 可能有多个订阅源，取第一个
                        source_name = quark_link_to_rss[share_url][0]["source_name"] if quark_link_to_rss[share_url] else ""

                    export_data["details"].append({
                        "标题": title[:50] + "..." if len(title) > 50 else title,
                        "订阅源": source_name,
                        "文件夹路径": path_info,
                        "大小(MB)": f"{size_mb:.2f}",
                        "浏览量": views,
                        "保存次数": saves,
                        "下载次数": downloads,
                        "创建时间": created_at,
                        "链接": share_url
                    })

                # 导出到Excel
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_to_excel(export_data, f"分享浏览统计_{timestamp}.xlsx")

        except Exception as e:
            custom_print(f"分析分享统计数据失败: {e}", error_msg=True)

    async def analyze_rss_source_stats(self, record_count: int = 100) -> None:
        """
        分析订阅源分享链接的浏览情况统计数据

        Args:
            record_count: 要查询的分享记录数量，默认为100

        功能：
        1. 查询哪个订阅源分享的链接经过转存、分享后浏览量最高
        2. 按订阅源统计浏览量、保存次数、下载次数
        3. 显示每个订阅源的最受欢迎分享
        4. 统计未转存过链接的订阅源
        5. 显示未转存链接最多的订阅源及其链接列表
        6. 显示从未获取到分享链接的订阅源
        """
        from datetime import datetime
        from collections import defaultdict
        from prettytable import PrettyTable

        # 分享统计API URL
        url = "https://drive-pc.quark.cn/1/clouddrive/share/mypage/detail"

        # 查询参数
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "uc_param_str": "",
            "_page": "1",
            "_size": str(record_count),  # 用户指定的记录数量
            "_order_field": "created_at",
            "_order_type": "desc",
            "_fetch_total": "1",
            "_fetch_notify_follow": "1"
        }

        try:
            # 1. 获取分享统计数据
            custom_print("正在获取分享统计数据...")
            async with await self.get_client() as client:
                response = await client.get(url, headers=self.headers, params=params)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"请求失败，状态码: {response.status_code}", error_msg=True)
                    return

                # 解析响应JSON
                try:
                    data = response.json()
                except Exception as e:
                    custom_print(f"解析响应JSON失败: {e}", error_msg=True)
                    return

                # 检查响应是否成功
                if data.get("status") != 200:
                    custom_print(f"API返回错误: {data.get('message', '未知错误')}", error_msg=True)
                    return

                # 提取分享记录
                if "data" not in data or "list" not in data["data"]:
                    custom_print("响应中没有找到分享记录列表", error_msg=True)
                    return

                all_shares = data["data"]["list"]
                total_count = data["metadata"].get("_total", 0)
                custom_print(f"找到 {len(all_shares)} 条分享记录，总共 {total_count} 条")

                # 过滤掉已删除的分享（status不等于1的记录）
                shares = [share for share in all_shares if share.get("status", 0) == 1]
                filtered_count = len(all_shares) - len(shares)

                if filtered_count > 0:
                    custom_print(f"已过滤 {filtered_count} 条已删除的分享记录")

                # 如果没有分享记录，直接返回
                if not shares:
                    custom_print("没有找到有效的分享记录", error_msg=True)
                    return

                # 2. 获取数据库中的RSS链接记录
                custom_print("正在从数据库获取RSS链接记录...")
                conn = sqlite3.connect('data/quark_links.db')
                cursor = conn.cursor()

                # 获取所有RSS链接记录
                cursor.execute("SELECT source_name, source_url, share_link, is_saved FROM rss_links")
                rss_links_records = cursor.fetchall()

                if not rss_links_records:
                    custom_print("数据库中没有找到RSS链接记录", error_msg=True)
                    conn.close()
                    return

                custom_print(f"从数据库中获取到 {len(rss_links_records)} 条RSS链接记录")

                # 3. 获取数据库中的分享记录
                cursor.execute("SELECT folder_id, folder_name, subfolder_id, subfolder_name, share_url FROM share_records WHERE status = 1")
                share_records = cursor.fetchall()

                if not share_records:
                    custom_print("数据库中没有找到分享记录", error_msg=True)
                    conn.close()
                    return

                custom_print(f"从数据库中获取到 {len(share_records)} 条分享记录")

                # 4. 建立数据关联
                # 创建映射：分享URL -> 分享统计数据
                share_url_to_stats = {}
                for share in shares:
                    share_url = share.get("share_url", "")
                    if share_url:
                        share_url_to_stats[share_url] = {
                            "title": share.get("title", "未知"),
                            "click_pv": share.get("click_pv", 0),
                            "save_pv": share.get("save_pv", 0),
                            "download_pv": share.get("download_pv", 0),
                            "size": share.get("size", 0),
                            "path_info": share.get("path_info", "未知"),
                            "created_at": share.get("created_at", 0)
                        }

                # 创建映射：夸克链接 -> RSS源信息
                quark_link_to_rss = {}
                for record in rss_links_records:
                    source_name, source_url, share_link, is_saved = record
                    if share_link not in quark_link_to_rss:
                        quark_link_to_rss[share_link] = []
                    quark_link_to_rss[share_link].append({
                        "source_name": source_name,
                        "source_url": source_url,
                        "is_saved": is_saved
                    })

                # 创建映射：文件夹ID -> 分享URL
                folder_to_share_url = {}
                for record in share_records:
                    folder_id, folder_name, subfolder_id, subfolder_name, share_url = record
                    folder_to_share_url[subfolder_id] = {
                        "folder_name": subfolder_name,
                        "parent_folder_name": folder_name,
                        "share_url": share_url
                    }

                # 5. 从数据库获取已保存的链接信息
                cursor.execute("SELECT url, folder_id, folder_name FROM saved_links WHERE status = 1")
                saved_links = cursor.fetchall()

                if not saved_links:
                    custom_print("数据库中没有找到已保存的链接", error_msg=True)
                    conn.close()
                    return

                custom_print(f"从数据库中获取到 {len(saved_links)} 条已保存的链接")

                # 创建映射：夸克链接 -> 保存文件夹
                quark_link_to_folder = {}
                for record in saved_links:
                    url, folder_id, folder_name = record
                    quark_link_to_folder[url] = {
                        "folder_id": folder_id,
                        "folder_name": folder_name
                    }

                conn.close()

                # 6. 分析数据，建立RSS源与分享统计的关联
                custom_print("\n正在分析数据，建立RSS源与分享统计的关联...")

                # 创建订阅源统计数据结构
                source_stats = defaultdict(lambda: {
                    "views": 0,
                    "saves": 0,
                    "downloads": 0,
                    "shares": 0,
                    "top_share": None,
                    "top_views": 0,
                    "source_url": "",
                    "links": []
                })

                # 遍历所有已保存的链接
                for quark_link, folder_info in quark_link_to_folder.items():
                    folder_id = folder_info["folder_id"]

                    # 检查该文件夹是否有分享记录
                    if folder_id in folder_to_share_url:
                        share_info = folder_to_share_url[folder_id]
                        share_url = share_info["share_url"]

                        # 检查该分享是否有统计数据
                        if share_url in share_url_to_stats:
                            stats = share_url_to_stats[share_url]

                            # 检查该链接是否来自RSS源
                            if quark_link in quark_link_to_rss:
                                rss_sources = quark_link_to_rss[quark_link]

                                # 对每个关联的RSS源进行统计
                                for rss_source in rss_sources:
                                    source_name = rss_source["source_name"]
                                    source_url = rss_source["source_url"]

                                    # 更新订阅源统计数据
                                    if source_name not in source_stats:
                                        source_stats[source_name] = {
                                            "views": 0,
                                            "saves": 0,
                                            "downloads": 0,
                                            "shares": 0,
                                            "top_share": None,
                                            "top_views": 0,
                                            "source_url": source_url,
                                            "links": []
                                        }

                                    source_stats[source_name]["views"] += stats["click_pv"]
                                    source_stats[source_name]["saves"] += stats["save_pv"]
                                    source_stats[source_name]["downloads"] += stats["download_pv"]
                                    source_stats[source_name]["shares"] += 1
                                    source_stats[source_name]["links"].append({
                                        "quark_link": quark_link,
                                        "share_url": share_url,
                                        "views": stats["click_pv"],
                                        "saves": stats["save_pv"],
                                        "downloads": stats["download_pv"],
                                        "title": stats["title"],
                                        "folder_name": share_info["folder_name"],
                                        "parent_folder_name": share_info["parent_folder_name"],
                                        "path_info": stats["path_info"],
                                        "size": stats["size"],
                                        "created_at": stats["created_at"]
                                    })

                                    # 更新最受欢迎的分享
                                    if stats["click_pv"] > source_stats[source_name]["top_views"]:
                                        source_stats[source_name]["top_share"] = {
                                            "quark_link": quark_link,
                                            "share_url": share_url,
                                            "views": stats["click_pv"],
                                            "saves": stats["save_pv"],
                                            "downloads": stats["download_pv"],
                                            "title": stats["title"],
                                            "folder_name": share_info["folder_name"],
                                            "parent_folder_name": share_info["parent_folder_name"],
                                            "path_info": stats["path_info"],
                                            "size": stats["size"],
                                            "created_at": stats["created_at"]
                                        }
                                        source_stats[source_name]["top_views"] = stats["click_pv"]

                # 7. 统计未转存过链接的订阅源
                custom_print("\n=== 未转存链接的订阅源统计 ===")

                # 查询所有RSS源记录
                cursor.execute("""
                    SELECT source_name, source_url, COUNT(*) as total_links,
                           SUM(CASE WHEN is_saved = 1 THEN 1 ELSE 0 END) as saved_links
                    FROM rss_links
                    GROUP BY source_name, source_url
                    ORDER BY total_links DESC
                """)

                rss_source_stats = cursor.fetchall()

                if not rss_source_stats:
                    custom_print("数据库中没有找到RSS源记录", error_msg=True)
                else:
                    # 创建未转存链接的订阅源统计表格
                    unsaved_table = PrettyTable()
                    unsaved_table.field_names = ["订阅源", "总链接数", "已转存数", "未转存数", "转存率", "订阅源URL"]
                    unsaved_table.align = "l"
                    unsaved_table.max_width["订阅源"] = 30
                    unsaved_table.max_width["订阅源URL"] = 50

                    # 统计未转存链接的订阅源
                    unsaved_sources = []
                    for source_name, source_url, total_links, saved_links in rss_source_stats:
                        unsaved_links = total_links - saved_links
                        save_rate = (saved_links / total_links * 100) if total_links > 0 else 0

                        # 添加到表格
                        unsaved_table.add_row([
                            source_name[:30],
                            total_links,
                            saved_links,
                            unsaved_links,
                            f"{save_rate:.2f}%",
                            source_url[:50]
                        ])

                    # 输出未转存链接的订阅源统计表格
                    custom_print(unsaved_table)

                    # 如果有未转存的链接，添加到未转存源列表
                    unsaved_sources = []
                    for source_name, source_url, total_links, saved_links in rss_source_stats:
                        unsaved_links = total_links - saved_links
                        save_rate = (saved_links / total_links * 100) if total_links > 0 else 0

                        if unsaved_links > 0:
                            unsaved_sources.append({
                                "source_name": source_name,
                                "source_url": source_url,
                                "total_links": total_links,
                                "saved_links": saved_links,
                                "unsaved_links": unsaved_links,
                                "save_rate": save_rate
                            })

                    # 输出未转存链接的订阅源统计
                    custom_print(unsaved_table)

                    # 输出未转存链接最多的订阅源
                    if unsaved_sources:
                        # 按未转存链接数量排序
                        unsaved_sources.sort(key=lambda x: x["unsaved_links"], reverse=True)
                        top_unsaved = unsaved_sources[0]

                        custom_print(f"\n未转存链接最多的订阅源: {top_unsaved['source_name']}")
                        custom_print(f"总链接数: {top_unsaved['total_links']}, 已转存: {top_unsaved['saved_links']}, 未转存: {top_unsaved['unsaved_links']}")
                        custom_print(f"转存率: {top_unsaved['save_rate']:.2f}%")
                        custom_print(f"订阅源URL: {top_unsaved['source_url']}")

                        # 查询该订阅源的未转存链接
                        cursor.execute("""
                            SELECT share_link, created_at
                            FROM rss_links
                            WHERE source_name = ? AND is_saved = 0
                            ORDER BY created_at DESC
                            LIMIT 5
                        """, (top_unsaved['source_name'],))

                        unsaved_links = cursor.fetchall()
                        if unsaved_links:
                            custom_print("\n该订阅源的部分未转存链接:")
                            for link, created_at in unsaved_links:
                                created_time = datetime.fromtimestamp(created_at).strftime("%Y-%m-%d %H:%M:%S")
                                custom_print(f"- {link} (添加时间: {created_time})")
                    else:
                        custom_print("\n所有订阅源的链接都已转存完成！")

                # 8. 输出分享统计分析结果
                if not source_stats:
                    custom_print("\n没有找到RSS源与分享统计的关联数据", error_msg=True)
                    return

                # 按浏览量排序
                sorted_sources = sorted(source_stats.items(), key=lambda x: x[1]["views"], reverse=True)

                custom_print(f"\n=== 订阅源分享统计 (共 {len(sorted_sources)} 个订阅源) ===")
                custom_print("详细信息将导出到Excel表格中，终端仅显示总览信息")

                # 准备导出数据
                export_data = {
                    "source_stats": [],
                    "source_details": []
                }

                # 添加订阅源统计数据
                for source_name, stats in sorted_sources:
                    top_share_title = stats["top_share"]["title"] if stats["top_share"] else "无"
                    top_views = stats["top_views"]

                    export_data["source_stats"].append({
                        "订阅源": source_name,
                        "订阅源URL": stats["source_url"],
                        "浏览量": stats["views"],
                        "保存次数": stats["saves"],
                        "下载次数": stats["downloads"],
                        "分享数": stats["shares"],
                        "最受欢迎分享": top_share_title[:50] + "..." if len(top_share_title) > 50 else top_share_title,
                        "最高浏览量": top_views
                    })

                # 显示最受欢迎的订阅源总览信息
                if sorted_sources:
                    top_source_name, top_source_stats = sorted_sources[0]
                    custom_print(f"\n=== 最受欢迎的订阅源: {top_source_name} ===")
                    custom_print(f"订阅源URL: {top_source_stats['source_url']}")
                    custom_print(f"总浏览量: {top_source_stats['views']}")
                    custom_print(f"总保存次数: {top_source_stats['saves']}")
                    custom_print(f"总下载次数: {top_source_stats['downloads']}")
                    custom_print(f"分享数量: {top_source_stats['shares']}")

                    # 添加最受欢迎订阅源的详情数据
                    custom_print("\n该订阅源的分享详情将导出到Excel表格中")

                    # 按浏览量排序
                    sorted_links = sorted(top_source_stats["links"], key=lambda x: x["views"], reverse=True)

                    for link in sorted_links:
                        title = link["title"]
                        folder_path = f"{link['parent_folder_name']}/{link['folder_name']}"
                        size_mb = link["size"] / (1024 * 1024)  # 转换为MB
                        views = link["views"]
                        saves = link["saves"]
                        downloads = link["downloads"]
                        created_at = datetime.fromtimestamp(link["created_at"] / 1000).strftime("%Y-%m-%d %H:%M:%S")
                        share_url = link["share_url"]

                        export_data["source_details"].append({
                            "标题": title[:50] + "..." if len(title) > 50 else title,
                            "文件夹": folder_path,
                            "大小(MB)": f"{size_mb:.2f}",
                            "浏览量": views,
                            "保存次数": saves,
                            "下载次数": downloads,
                            "创建时间": created_at,
                            "分享链接": share_url
                        })

                # 添加未转存链接的订阅源统计数据
                export_data["unsaved_links"] = []

                # 查询所有RSS源记录
                # 使用新的数据库连接，避免使用已关闭的连接
                conn_rss = sqlite3.connect('data/quark_links.db')
                cursor_rss = conn_rss.cursor()

                cursor_rss.execute("""
                    SELECT source_name, source_url, COUNT(*) as total_links,
                           SUM(CASE WHEN is_saved = 1 THEN 1 ELSE 0 END) as saved_links
                    FROM rss_links
                    GROUP BY source_name, source_url
                    ORDER BY total_links DESC
                """)

                rss_source_stats = cursor_rss.fetchall()

                # 输出未转存链接的订阅源统计
                custom_print("\n=== 未转存链接的订阅源统计 ===")
                if not rss_source_stats:
                    custom_print("数据库中没有找到RSS源记录", error_msg=True)
                else:
                    # 创建未转存链接的订阅源统计表格
                    unsaved_table = PrettyTable()
                    unsaved_table.field_names = ["订阅源", "总链接数", "已转存数", "未转存数", "转存率", "订阅源URL"]
                    unsaved_table.align = "l"
                    unsaved_table.max_width["订阅源"] = 30
                    unsaved_table.max_width["订阅源URL"] = 50

                    # 统计未转存链接的订阅源
                    for source_name, source_url, total_links, saved_links in rss_source_stats:
                        unsaved_links = total_links - saved_links
                        save_rate = (saved_links / total_links * 100) if total_links > 0 else 0

                        # 添加到导出数据
                        export_data["unsaved_links"].append({
                            "订阅源": source_name,
                            "订阅源URL": source_url,
                            "总链接数": total_links,
                            "已转存数": saved_links,
                            "未转存数": unsaved_links,
                            "转存率": f"{save_rate:.2f}%"
                        })

                        # 如果有未转存的链接，添加到未转存源列表
                        if unsaved_links > 0:
                            unsaved_sources.append({
                                "source_name": source_name,
                                "source_url": source_url,
                                "total_links": total_links,
                                "saved_links": saved_links,
                                "unsaved_links": unsaved_links,
                                "save_rate": save_rate
                            })

                    # 如果有未转存链接的订阅源，添加其未转存链接列表
                    if unsaved_sources:
                        # 按未转存链接数量排序
                        unsaved_sources.sort(key=lambda x: x["unsaved_links"], reverse=True)
                        top_unsaved = unsaved_sources[0]

                        # 查询该订阅源的未转存链接
                        cursor_rss.execute("""
                            SELECT share_link, created_at
                            FROM rss_links
                            WHERE source_name = ? AND is_saved = 0
                            ORDER BY created_at DESC
                        """, (top_unsaved['source_name'],))

                        unsaved_links = cursor_rss.fetchall()

                        # 添加到导出数据
                        export_data["top_unsaved_links"] = []
                        for link, created_at in unsaved_links:
                            created_time = datetime.fromtimestamp(created_at).strftime("%Y-%m-%d %H:%M:%S")
                            export_data["top_unsaved_links"].append({
                                "链接": link,
                                "添加时间": created_time,
                                "订阅源": top_unsaved['source_name']
                            })

                # 9. 统计从未获取到分享链接的订阅源
                custom_print("\n=== 从未获取到分享链接的订阅源统计 ===")

                # 获取配置文件中的所有订阅源
                configured_sources = []
                if self.rss_config and "sources" in self.rss_config:
                    for source in self.rss_config["sources"]:
                        if "url" in source:
                            configured_sources.append(source["url"])

                # 如果还有旧的rss_sources配置，也加入
                if self.rss_config and "rss_sources" in self.rss_config:
                    for source_url in self.rss_config["rss_sources"]:
                        if source_url not in configured_sources:
                            configured_sources.append(source_url)

                if not configured_sources:
                    custom_print("配置文件中没有找到订阅源", error_msg=True)
                else:
                    custom_print(f"配置文件中共有 {len(configured_sources)} 个订阅源")

                    # 获取数据库中已有记录的订阅源URL
                    cursor_rss.execute("SELECT DISTINCT source_url FROM rss_links")
                    db_sources = [row[0] for row in cursor_rss.fetchall()]

                    # 找出在配置中存在但在数据库中没有记录的订阅源
                    never_fetched_sources = []
                    for source_url in configured_sources:
                        if source_url not in db_sources:
                            never_fetched_sources.append(source_url)

                    if never_fetched_sources:
                        custom_print(f"发现 {len(never_fetched_sources)} 个从未获取到分享链接的订阅源:")

                        # 创建表格
                        never_fetched_table = PrettyTable()
                        never_fetched_table.field_names = ["订阅源URL", "状态"]
                        never_fetched_table.align = "l"
                        never_fetched_table.max_width["订阅源URL"] = 80

                        # 添加到导出数据
                        export_data["never_fetched_sources"] = []

                        for source_url in never_fetched_sources:
                            # 添加到表格
                            never_fetched_table.add_row([
                                source_url[:80],
                                "从未获取到分享链接"
                            ])

                            # 添加到导出数据
                            export_data["never_fetched_sources"].append({
                                "订阅源URL": source_url,
                                "状态": "从未获取到分享链接"
                            })

                        # 输出表格
                        custom_print(never_fetched_table)
                    else:
                        custom_print("所有配置的订阅源都已获取到分享链接")

                # 关闭RSS数据库连接
                conn_rss.close()

                # 导出到Excel
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_to_excel(export_data, f"订阅源分享统计_{timestamp}.xlsx")

        except Exception as e:
            custom_print(f"分析订阅源分享统计数据失败: {e}", error_msg=True)

    def is_folder_shared(self, subfolder_id: str) -> bool:
        """
        检查文件夹是否已经分享过

        Args:
            subfolder_id: 子文件夹ID

        Returns:
            bool: 如果文件夹已经分享过且分享成功，返回True，否则返回False
        """
        # 确保表存在
        self.ensure_share_records_table()

        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 查询是否有成功的分享记录
        cursor.execute(
            "SELECT id FROM share_records WHERE subfolder_id = ? AND status = 1 AND share_url != ''",
            (subfolder_id,)
        )
        result = cursor.fetchone()
        conn.close()

        return result is not None

    async def check_unsaved_links(self, folder_id: str = None) -> int:
        """
        检查数据库中未转存的链接并尝试转存

        Args:
            folder_id: 转存目标文件夹ID，如果为None则使用当前文件夹

        Returns:
            int: 成功转存的链接数量
        """
        custom_print("=" * 50)
        custom_print("开始检查未转存的链接...")
        custom_print("=" * 50)

        # 如果未提供文件夹ID，使用当前文件夹
        if not folder_id:
            folder_id = self.pdir_id

        # 查询数据库中未成功转存的链接
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 查询未成功转存且重试次数小于3次的链接
        cursor.execute("""
            SELECT url, title, retry_count
            FROM saved_links
            WHERE transfer_status != 1 AND retry_count < 3
            ORDER BY id DESC
        """)
        unsaved_links = cursor.fetchall()
        conn.close()

        if not unsaved_links:
            custom_print("没有找到需要转存的链接")
            return 0

        custom_print(f"找到 {len(unsaved_links)} 个未成功转存的链接")

        # 尝试转存这些链接
        success_count = 0
        for url, title, retry_count in unsaved_links:
            custom_print(f"尝试转存链接: {url}, 标题: {title}, 已重试次数: {retry_count}")

            try:
                # 转存链接
                transfer_result = await self.run(url, folder_id)

                # 检查转存结果
                if transfer_result:
                    # 检查transfer_result是否是字典类型
                    if isinstance(transfer_result, dict) and transfer_result.get("success"):
                        # 更新数据库中的转存状态为成功
                        self.save_link_to_db(url, title, folder_id, self.dir_name,
                                           transfer_status=1)
                        # 更新RSS链接记录的保存状态
                        self.save_rss_link_record("未知来源", "未知URL", url, is_saved=1)
                        success_count += 1
                        custom_print(f"链接转存成功: {url}")
                    elif isinstance(transfer_result, bool) and transfer_result:
                        # 如果transfer_result是布尔值True，也视为成功
                        self.save_link_to_db(url, title, folder_id, self.dir_name,
                                           transfer_status=1)
                        # 更新RSS链接记录的保存状态
                        self.save_rss_link_record("未知来源", "未知URL", url, is_saved=1)
                        success_count += 1
                        custom_print(f"链接转存成功: {url}")
                    else:
                        # 更新数据库中的转存状态为失败
                        self.save_link_to_db(url, title, folder_id, self.dir_name,
                                           transfer_status=2,
                                           error_msg=f"转存失败，可能是网盘中已存在该文件，结果类型: {type(transfer_result)}")
                        custom_print(f"链接转存失败: {url}")
                else:
                    # 更新数据库中的转存状态为失败
                    self.save_link_to_db(url, title, folder_id, self.dir_name,
                                       transfer_status=2,
                                       error_msg="转存失败，可能是网盘中已存在该文件")
                    custom_print(f"链接转存失败: {url}")
            except Exception as e:
                error_msg = str(e)
                custom_print(f"转存链接失败: {error_msg}", error_msg=True)

                # 检查是否是永久性错误（如用户等级限制）
                permanent_error = False
                if hasattr(e, 'permanent_error'):
                    permanent_error = getattr(e, 'permanent_error', False)
                elif '单次转存文件个数超出用户等级限制' in error_msg:
                    permanent_error = True

                # 更新数据库中的转存状态
                if permanent_error:
                    # 对于永久性错误，设置重试次数为最大值，避免再次尝试
                    self.save_link_to_db(url, title, folder_id, self.dir_name,
                                       transfer_status=2,
                                       error_msg=f"永久性错误: {error_msg}")
                    # 设置重试次数为最大值
                    conn = sqlite3.connect('data/quark_links.db')
                    cursor = conn.cursor()
                    cursor.execute("UPDATE saved_links SET retry_count = 999 WHERE url = ?", (url,))
                    conn.commit()
                    conn.close()
                    custom_print(f"链接 {url} 因永久性错误被标记为不再重试", error_msg=True)
                else:
                    # 对于临时性错误，正常更新状态
                    self.save_link_to_db(url, title, folder_id, self.dir_name,
                                       transfer_status=2,
                                       error_msg=error_msg)

        custom_print(f"未转存链接处理完成: 共处理 {len(unsaved_links)} 个链接，成功转存 {success_count} 个")
        return success_count

    async def check_unsaved_rss_links(self, folder_id: str = None) -> int:
        """
        检查rss_links表中未转存的链接并尝试转存

        Args:
            folder_id: 转存目标文件夹ID，如果为None则使用当前文件夹

        Returns:
            int: 成功转存的链接数量
        """
        custom_print("=" * 50)
        custom_print("开始检查RSS源中未转存的链接...")
        custom_print("=" * 50)

        # 如果未提供文件夹ID，使用当前文件夹
        if not folder_id:
            folder_id = self.pdir_id

        # 查询rss_links表中未成功转存的链接
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 确保rss_links表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS rss_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT,
                source_url TEXT,
                share_link TEXT UNIQUE,
                is_saved INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.commit()

        # 查询未成功转存的链接
        cursor.execute("""
            SELECT id, source_name, source_url, share_link
            FROM rss_links
            WHERE is_saved = 0
            ORDER BY id DESC
        """)
        unsaved_links = cursor.fetchall()
        conn.close()

        if not unsaved_links:
            custom_print("没有找到RSS源中需要转存的链接")
            return 0

        custom_print(f"找到 {len(unsaved_links)} 个RSS源中未转存的链接")

        # 尝试转存这些链接
        success_count = 0
        for id, source_name, source_url, share_link in unsaved_links:
            # 使用source_url变量，避免IDE警告
            custom_print(f"尝试转存RSS源链接: {share_link}, 来源: {source_name}, 来源URL: {source_url}")

            try:
                # 检查链接是否已经在saved_links表中成功转存
                conn = sqlite3.connect('data/quark_links.db')
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT id FROM saved_links WHERE url = ? AND transfer_status = 1",
                    (share_link,)
                )
                already_saved = cursor.fetchone()
                conn.close()

                if already_saved:
                    # 链接已经在saved_links表中成功转存，更新rss_links表
                    custom_print(f"链接已在saved_links表中成功转存，更新RSS源记录: {share_link}")
                    conn = sqlite3.connect('data/quark_links.db')
                    cursor = conn.cursor()
                    cursor.execute(
                        "UPDATE rss_links SET is_saved = 1 WHERE id = ?",
                        (id,)
                    )
                    conn.commit()
                    conn.close()
                    success_count += 1
                    continue

                # 提取链接标题
                title = ""
                try:
                    # 尝试从saved_links表中获取标题
                    conn = sqlite3.connect('data/quark_links.db')
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT title FROM saved_links WHERE url = ? LIMIT 1",
                        (share_link,)
                    )
                    title_result = cursor.fetchone()
                    conn.close()

                    if title_result and title_result[0]:
                        title = title_result[0]
                    else:
                        # 如果没有找到标题，使用来源名称作为标题
                        title = f"来自{source_name}的分享"
                except Exception as e:
                    custom_print(f"获取链接标题失败: {e}", error_msg=True)
                    title = f"来自{source_name}的分享"

                # 转存链接
                transfer_result = await self.run(share_link, folder_id)

                # 检查转存结果
                if transfer_result:
                    # 检查transfer_result是否是字典类型
                    if isinstance(transfer_result, dict) and transfer_result.get("success"):
                        # 更新数据库中的转存状态为成功
                        self.save_link_to_db(share_link, title, folder_id, self.dir_name,
                                           transfer_status=1)
                        # 更新RSS链接记录的保存状态
                        conn = sqlite3.connect('data/quark_links.db')
                        cursor = conn.cursor()
                        cursor.execute(
                            "UPDATE rss_links SET is_saved = 1 WHERE id = ?",
                            (id,)
                        )
                        conn.commit()
                        conn.close()
                        success_count += 1
                        custom_print(f"RSS源链接转存成功: {share_link}")
                    elif isinstance(transfer_result, bool) and transfer_result:
                        # 如果transfer_result是布尔值True，也视为成功
                        self.save_link_to_db(share_link, title, folder_id, self.dir_name,
                                           transfer_status=1)
                        # 更新RSS链接记录的保存状态
                        conn = sqlite3.connect('data/quark_links.db')
                        cursor = conn.cursor()
                        cursor.execute(
                            "UPDATE rss_links SET is_saved = 1 WHERE id = ?",
                            (id,)
                        )
                        conn.commit()
                        conn.close()
                        success_count += 1
                        custom_print(f"RSS源链接转存成功: {share_link}")
                    else:
                        # 更新数据库中的转存状态为失败
                        self.save_link_to_db(share_link, title, folder_id, self.dir_name,
                                           transfer_status=2,
                                           error_msg=f"转存失败，可能是网盘中已存在该文件，结果类型: {type(transfer_result)}")
                        custom_print(f"RSS源链接转存失败: {share_link}")
                else:
                    # 更新数据库中的转存状态为失败
                    self.save_link_to_db(share_link, title, folder_id, self.dir_name,
                                       transfer_status=2,
                                       error_msg="转存失败，可能是网盘中已存在该文件")
                    custom_print(f"RSS源链接转存失败: {share_link}")
            except Exception as e:
                error_msg = str(e)
                custom_print(f"转存RSS源链接失败: {error_msg}", error_msg=True)

        custom_print(f"RSS源未转存链接处理完成: 共处理 {len(unsaved_links)} 个链接，成功转存 {success_count} 个")
        return success_count

    async def check_unshared_folders(self, parent_folder_id: str = None) -> int:
        """
        检查数据库中已转存但未分享的文件夹并尝试分享

        Args:
            parent_folder_id: 父文件夹ID，如果为None则使用当前文件夹

        Returns:
            int: 成功分享的文件夹数量
        """
        custom_print("=" * 50)
        custom_print("开始检查未分享的文件夹...")
        custom_print("=" * 50)

        # 如果未提供父文件夹ID，使用当前文件夹
        if not parent_folder_id:
            parent_folder_id = self.pdir_id

        # 获取文件夹下的子文件夹列表
        try:
            file_list_data = await self.get_sorted_file_list(pdir_fid=parent_folder_id)

            if not file_list_data:
                custom_print("获取文件夹列表失败或文件夹为空")
                return 0

            # 确保file_list_data是列表类型
            if not isinstance(file_list_data, list):
                custom_print(f"获取到的文件夹列表格式不正确: {type(file_list_data)}")
                return 0

            # 过滤出文件夹类型的项目，确保每个项目是字典类型
            folders = []
            for item in file_list_data:
                if isinstance(item, dict) and item.get("dir") == 1:
                    folders.append(item)
                elif isinstance(item, dict):
                    # 如果是字典但没有dir字段或dir不等于1，可能是文件
                    continue
                else:
                    # 记录非字典类型的项目，帮助调试
                    custom_print(f"跳过非字典类型的项目: {type(item)}: {item}", error_msg=True)
        except Exception as e:
            custom_print(f"获取文件夹列表时出错: {e}", error_msg=True)
            return 0

        if not folders:
            custom_print("没有找到子文件夹")
            return 0

        custom_print(f"找到 {len(folders)} 个子文件夹")

        # 获取分享设置
        share_settings = self.rss_config.get("share_settings", {})
        url_type = share_settings.get("url_type", 1)  # 默认为公开分享
        expired_type = share_settings.get("expired_type", 4)  # 默认为永久有效
        password = share_settings.get("password", "")  # 默认无密码

        # 显示分享设置
        url_type_str = "公开分享" if url_type == 1 else "私密分享"
        expired_type_map = {1: "1天", 2: "7天", 3: "30天", 4: "永久"}
        expired_str = expired_type_map.get(expired_type, "未知")
        password_str = "已设置" if password else "未设置"

        custom_print(f"分享设置: 链接类型={url_type_str}, 有效期={expired_str}, 提取码={password_str}")

        # 创建保存分享链接的文件夹
        os.makedirs('auto_share', exist_ok=True)

        # 分享链接保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        share_file_path = f'auto_share/unshared_folders_{timestamp}_share_links.txt'

        # 尝试分享这些文件夹
        success_count = 0
        for folder in folders:
            folder_id = folder.get("fid")
            folder_name = folder.get("file_name")

            # 检查文件夹是否已经分享过
            if self.is_folder_shared(folder_id):
                custom_print(f"文件夹 {folder_name} 已经分享过，跳过分享")
                # 获取之前的分享链接
                conn = sqlite3.connect('data/quark_links.db')
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT share_url FROM share_records WHERE subfolder_id = ? AND status = 1 AND share_url != '' ORDER BY id DESC LIMIT 1",
                    (folder_id,)
                )
                result = cursor.fetchone()
                conn.close()

                if result and result[0]:
                    # 将之前的分享链接添加到文件中
                    with open(share_file_path, 'a', encoding='utf-8') as f:
                        f.write(f"{result[0]}\n")
                    success_count += 1

                continue

            custom_print(f"尝试分享文件夹: {folder_name}")

            try:
                # 分享文件夹
                share_result = await self.share_folder(
                    folder_id, folder_name, url_type, expired_type, password)

                if share_result and share_result.get("success"):
                    share_url = share_result.get("share_url", "")
                    if share_url:
                        # 将分享链接保存到文件
                        with open(share_file_path, 'a', encoding='utf-8') as f:
                            f.write(f"{share_url}\n")
                        success_count += 1
                        custom_print(f"文件夹 {folder_name} 分享成功: {share_url}")
                    else:
                        custom_print(f"文件夹 {folder_name} 分享失败: 未获取到分享链接", error_msg=True)
                else:
                    custom_print(f"文件夹 {folder_name} 分享失败: {share_result.get('message', '未知错误')}", error_msg=True)
            except Exception as e:
                custom_print(f"分享文件夹 {folder_name} 失败: {e}", error_msg=True)

        custom_print(f"未分享文件夹处理完成: 共处理 {len(folders)} 个文件夹，成功分享 {success_count} 个")

        if success_count > 0:
            custom_print(f"分享链接已保存到文件: {share_file_path}")

        return success_count

    def fix_empty_rss_source_records(self) -> int:
        """
        修复RSS链接记录中source_name和source_url为空的记录

        Returns:
            int: 修复的记录数量
        """
        custom_print("开始修复RSS链接记录中source_name和source_url为空的记录...")

        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 查询空记录数量
        cursor.execute("SELECT COUNT(*) FROM rss_links WHERE source_name = '' OR source_url = ''")
        empty_count = cursor.fetchone()[0]

        if empty_count == 0:
            custom_print("没有找到需要修复的记录")
            conn.close()
            return 0

        custom_print(f"找到 {empty_count} 条需要修复的记录")

        # 更新空记录
        cursor.execute(
            """UPDATE rss_links
               SET source_name = '未知来源', source_url = '未知URL'
               WHERE source_name = '' OR source_url = ''"""
        )

        # 获取更新的行数
        updated_rows = cursor.rowcount

        conn.commit()
        conn.close()

        custom_print(f"成功修复 {updated_rows} 条记录")
        return updated_rows

    async def get_client(self, force_proxy: bool = False) -> httpx.AsyncClient:
        """获取httpx客户端，支持代理配置

        Args:
            force_proxy: 是否强制使用代理，即使代理未启用
        """
        try:
            # 确保rss_config存在
            if not hasattr(self, 'rss_config') or self.rss_config is None:
                custom_print("RSS配置未加载，尝试重新加载", error_msg=True)
                self.rss_config = self.load_rss_config()

            # 检查是否启用代理或强制使用代理
            proxy_config = self.rss_config.get("proxy", {}) if self.rss_config else {}
            if force_proxy or proxy_config.get("enabled", False):
                http_proxy = proxy_config.get("http")
                if http_proxy:
                    custom_print(f"使用代理: {http_proxy}")
                    proxies = {
                        "http://": http_proxy,
                        "https://": proxy_config.get("https", http_proxy)
                    }
                    return httpx.AsyncClient(proxies=proxies)
                else:
                    custom_print("代理配置不完整，使用直连", error_msg=True)
                    return httpx.AsyncClient()
            else:
                return httpx.AsyncClient()
        except Exception as e:
            custom_print(f"创建HTTP客户端失败: {e}，使用直连", error_msg=True)
            return httpx.AsyncClient()

    async def get_client_with_retry(self, max_retries: int = 2) -> httpx.AsyncClient:
        """获取httpx客户端，带重试机制

        首先尝试不使用代理，如果请求失败则自动切换到代理

        Args:
            max_retries: 最大重试次数
        """
        try:
            # 第一次尝试不使用代理
            if max_retries <= 0:
                # 如果不允许重试，则根据配置决定是否使用代理
                try:
                    # 确保rss_config存在
                    if not hasattr(self, 'rss_config') or self.rss_config is None:
                        custom_print("RSS配置未加载，尝试重新加载", error_msg=True)
                        self.rss_config = self.load_rss_config()

                    proxy_config = self.rss_config.get("proxy", {}) if self.rss_config else {}
                    if proxy_config.get("enabled", False):
                        return await self.get_client(force_proxy=True)
                    else:
                        return await self.get_client(force_proxy=False)
                except Exception as e:
                    custom_print(f"获取代理配置失败: {e}，使用直连", error_msg=True)
                    return await self.get_client(force_proxy=False)

            # 创建一个客户端上下文管理器，可以在请求失败时自动切换到代理
            class ClientWithRetry:
                def __init__(self, manager, max_retries):
                    self.manager = manager
                    self.max_retries = max_retries
                    self.client = None
                    self.using_proxy = False
                    self.retry_count = 0

                async def __aenter__(self):
                    # 首次尝试不使用代理
                    self.client = await self.manager.get_client(force_proxy=False)
                    return self

                async def __aexit__(self, exc_type=None, exc_val=None, exc_tb=None):
                    # 使用参数变量，避免IDE警告
                    if exc_type:
                        custom_print(f"异常类型: {exc_type}")
                    if exc_val:
                        custom_print(f"异常值: {exc_val}")
                    if exc_tb:
                        custom_print(f"异常回溯: {exc_tb}")

                    if self.client:
                        await self.client.aclose()

                async def request(self, method, url, **kwargs):
                    """发送请求，失败时自动重试并使用代理"""
                    timeout = kwargs.get('timeout', httpx.Timeout(60.0, connect=60.0))
                    kwargs['timeout'] = timeout

                    while True:
                        try:
                            if method.lower() == 'get':
                                return await self.client.get(url, **kwargs)
                            elif method.lower() == 'post':
                                return await self.client.post(url, **kwargs)
                            elif method.lower() == 'stream':
                                return await self.client.stream('GET', url, **kwargs)
                            else:
                                raise ValueError(f"不支持的请求方法: {method}")
                        except (httpx.ConnectError, httpx.ConnectTimeout, httpx.ReadTimeout, httpx.HTTPError) as e:
                            self.retry_count += 1
                            if self.retry_count > self.max_retries:
                                raise  # 超过最大重试次数，抛出异常

                            # 关闭当前客户端
                            await self.client.aclose()

                            # 切换到代理
                            custom_print(f"请求失败: {e}，尝试使用代理重试 ({self.retry_count}/{self.max_retries})")
                            self.client = await self.manager.get_client(force_proxy=True)
                            self.using_proxy = True

            return ClientWithRetry(self, max_retries)
        except Exception as e:
            custom_print(f"创建重试客户端失败: {e}，使用普通客户端", error_msg=True)
            return await self.get_client(force_proxy=False)

    async def get_stoken(self, pwd_id: str) -> str:
        """获取stoken，如果失败则返回空字符串"""
        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
            '__dt': random.randint(100, 9999),
            '__t': get_timestamp(13),
        }
        api = f"https://drive-pc.quark.cn/1/clouddrive/share/sharepage/token"
        data = {"pwd_id": pwd_id, "passcode": ""}

        try:
            # 直接使用httpx客户端，不使用重试机制
            async with httpx.AsyncClient() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post(api, json=data, params=params, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取stoken失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return ''

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取stoken失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return ''

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取stoken失败: 服务器返回空响应", error_msg=True)
                    return ''

                # 检查状态码和数据
                if json_data.get('status') == 200 and json_data.get('data'):
                    stoken = json_data["data"].get("stoken", '')
                    if not stoken:
                        custom_print("获取stoken失败: 响应中没有stoken字段", error_msg=True)
                    return stoken
                else:
                    message = json_data.get('message', '未知错误')
                    custom_print(f"文件转存失败，{message}", error_msg=True)
                    return ''
        except Exception as e:
            custom_print(f"获取stoken失败: {e}", error_msg=True)
            return ''

    async def get_detail(self, pwd_id: str, stoken: str, pdir_fid: str = '0') -> Tuple[
                str, List[Dict[str, Union[int, str]]]]:
        """获取文件详情，如果失败则返回空列表"""
        api = f"https://drive-pc.quark.cn/1/clouddrive/share/sharepage/detail"
        page = 1
        file_list: List[Dict[str, Union[int, str]]] = []

        try:
            # 直接使用httpx客户端，不使用重试机制
            async with httpx.AsyncClient() as client:
                while True:
                    params = {
                        'pr': 'ucpro',
                        'fr': 'pc',
                        'uc_param_str': '',
                        "pwd_id": pwd_id,
                        "stoken": stoken,
                        'pdir_fid': pdir_fid,
                        'force': '0',
                        "_page": str(page),
                        '_size': '50',
                        '_sort': 'file_type:asc,updated_at:desc',
                        '__dt': random.randint(200, 9999),
                        '__t': get_timestamp(13),
                    }

                    timeout = httpx.Timeout(60.0, connect=60.0)
                    response = await client.get(api, headers=self.headers, params=params, timeout=timeout)

                    # 检查响应状态码
                    if response.status_code != 200:
                        custom_print(f"获取文件详情失败: HTTP状态码 {response.status_code}", error_msg=True)
                        return "0", []

                    # 尝试解析JSON响应
                    try:
                        json_data = response.json()
                    except Exception as e:
                        custom_print(f"获取文件详情失败: 无法解析JSON响应 - {e}", error_msg=True)
                        return "0", []

                    # 检查响应是否有效
                    if not json_data:
                        custom_print("获取文件详情失败: 服务器返回空响应", error_msg=True)
                        return "0", []

                    # 检查data字段是否存在
                    if 'data' not in json_data:
                        custom_print(f"获取文件详情失败: 响应中没有data字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                        return "0", []

                    # 检查metadata字段是否存在
                    if 'metadata' not in json_data:
                        custom_print(f"获取文件详情失败: 响应中没有metadata字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                        return "0", []

                    is_owner = json_data['data'].get('is_owner', 0)
                    _total = json_data['metadata'].get('_total', 0)
                    if _total < 1:
                        return is_owner, file_list

                    _size = json_data['metadata'].get('_size', 0)  # 每页限制数量
                    _count = json_data['metadata'].get('_count', 0)  # 当前页数量

                    _list = json_data["data"].get("list", [])

                    for file in _list:
                        d: Dict[str, Union[int, str]] = {
                            "fid": file.get("fid", ""),
                            "file_name": file.get("file_name", ""),
                            "file_type": file.get("file_type", ""),
                            "dir": file.get("dir", False),
                            "pdir_fid": file.get("pdir_fid", ""),
                            "include_items": file.get("include_items", ""),
                            "share_fid_token": file.get("share_fid_token", ""),
                            "status": file.get("status", 0)
                        }
                        file_list.append(d)
                    if _total <= _size or _count < _size:
                        return is_owner, file_list

                    page += 1
        except Exception as e:
            custom_print(f"获取文件详情失败: {e}", error_msg=True)
            return "0", []

    async def get_sorted_file_list(self, pdir_fid='0', page='1', size='100', fetch_total='false',
                                   sort='') -> List[Dict[str, Any]]:
        """
        获取排序的文件列表

        Args:
            pdir_fid: 父文件夹ID
            page: 页码
            size: 每页大小
            fetch_total: 是否获取总数
            sort: 排序方式

        Returns:
            List[Dict[str, Any]]: 文件列表，如果失败则返回空列表
        """
        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
            'pdir_fid': pdir_fid,
            '_page': page,
            '_size': size,
            '_fetch_total': fetch_total,
            '_fetch_sub_dirs': '1',
            '_sort': sort,
            '__dt': random.randint(100, 9999),
            '__t': get_timestamp(13),
        }

        try:
            # 直接使用httpx客户端，不使用重试机制
            async with httpx.AsyncClient() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.get('https://drive-pc.quark.cn/1/clouddrive/file/sort',
                                          params=params, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取文件列表失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return []

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取文件列表失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return []

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取文件列表失败: 服务器返回空响应", error_msg=True)
                    return []

                # 确保返回的数据结构完整
                if 'data' not in json_data:
                    custom_print("获取文件列表失败: 响应中没有data字段", error_msg=True)
                    return []

                if 'list' not in json_data['data']:
                    custom_print("获取文件列表失败: 响应中没有list字段", error_msg=True)
                    return []

                # 返回文件列表
                file_list = json_data['data']['list']

                # 确保每个项目都是字典类型
                valid_file_list = []
                for item in file_list:
                    if isinstance(item, dict):
                        valid_file_list.append(item)
                    else:
                        custom_print(f"跳过非字典类型的项目: {type(item)}: {item}", error_msg=True)

                return valid_file_list
        except Exception as e:
            custom_print(f"获取文件列表失败: {e}", error_msg=True)
            return []

    async def get_user_info(self) -> str:
        """获取用户信息，返回用户昵称"""
        params = {
            'fr': 'pc',
            'platform': 'pc',
        }

        # 直接使用httpx客户端，不使用重试机制
        try:
            # 确保cookies有效
            if not self.cookies:
                custom_print("Cookie为空，无法获取用户信息", error_msg=True)
                self.clear_cookies_and_exit()

            # 使用直接的httpx客户端
            async with httpx.AsyncClient() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.get('https://pan.quark.cn/account/info',
                                          params=params, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取用户信息失败: HTTP状态码 {response.status_code}", error_msg=True)
                    self.clear_cookies_and_exit()

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取用户信息失败: 无法解析JSON响应 - {e}", error_msg=True)
                    self.clear_cookies_and_exit()

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取用户信息失败: 服务器返回空响应", error_msg=True)
                    self.clear_cookies_and_exit()

                # 检查data字段是否存在
                if 'data' not in json_data:
                    custom_print(f"获取用户信息失败: 响应中没有data字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    self.clear_cookies_and_exit()

                # 检查data字段是否为None
                if json_data['data'] is None:
                    custom_print(f"获取用户信息失败: data字段为空 - {json_data.get('message', '未知错误')}", error_msg=True)
                    self.clear_cookies_and_exit()

                # 检查nickname字段是否存在
                if 'nickname' not in json_data['data']:
                    custom_print("获取用户信息失败: 无法获取用户昵称", error_msg=True)
                    self.clear_cookies_and_exit()

                # 获取昵称
                nickname = json_data['data']['nickname']
                return nickname
        except Exception as e:
            custom_print(f"获取用户信息失败: {e}", error_msg=True)
            self.clear_cookies_and_exit()

    def clear_cookies_and_exit(self):
        """清除cookies并退出程序"""
        custom_print("Cookie可能已过期，正在清除...", error_msg=True)
        input("请重新运行本程序，然后在弹出的浏览器中登录夸克账号")
        with open(f'{CONFIG_DIR}/cookies.txt', 'w', encoding='utf-8'):
            pass
        sys.exit(-1)

    async def create_dir(self, pdir_name='新建文件夹', pdir_fid='0') -> Optional[str]:
        """创建文件夹，返回文件夹ID"""
        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
            '__dt': random.randint(100, 9999),
            '__t': get_timestamp(13),
        }

        json_data = {
            'pdir_fid': pdir_fid,
            'file_name': pdir_name,
            'dir_path': '',
            'dir_init_lock': False,
        }

        try:
            # 直接使用httpx客户端，不使用重试机制
            async with httpx.AsyncClient() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post('https://drive-pc.quark.cn/1/clouddrive/file',
                                           params=params, json=json_data, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"创建文件夹失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return None

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"创建文件夹失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return None

                # 检查响应是否有效
                if not json_data:
                    custom_print("创建文件夹失败: 服务器返回空响应", error_msg=True)
                    return None

                # 处理响应
                if json_data.get("code") == 0:
                    custom_print(f'在{"根目录" if pdir_fid == "0" else "指定目录"}下 {pdir_name} 文件夹创建成功！')

                    # 检查data字段是否存在
                    if 'data' not in json_data or not json_data['data']:
                        custom_print("创建文件夹失败: 响应中没有data字段", error_msg=True)
                        return None

                    # 检查fid字段是否存在
                    if 'fid' not in json_data['data']:
                        custom_print("创建文件夹失败: 响应中没有fid字段", error_msg=True)
                        return None

                    folder_id = json_data["data"]["fid"]
                    if pdir_fid == '0':  # 只有在根目录创建文件夹时才更新配置
                        new_config = {'user': self.user, 'pdir_id': folder_id, 'dir_name': pdir_name}
                        save_config(f'{CONFIG_DIR}/config.json', content=json.dumps(new_config, ensure_ascii=False))
                        global to_dir_id
                        to_dir_id = folder_id
                        custom_print(f"自动将保存目录切换至 {pdir_name} 文件夹")
                    return folder_id
                elif json_data.get("code") == 23008:
                    custom_print(f'文件夹同名冲突，尝试查找已存在的同名文件夹')
                    # 尝试查找已存在的同名文件夹
                    file_list_data = await self.get_sorted_file_list(pdir_fid=pdir_fid)

                    # 检查返回的数据结构
                    if not file_list_data:
                        custom_print("获取文件列表失败: 未获取到文件列表", error_msg=True)
                        return None

                    for item in file_list_data:
                        if item.get('dir') and item.get('file_name') == pdir_name:
                            custom_print(f'找到已存在的同名文件夹，ID: {item["fid"]}')
                            return item["fid"]
                    custom_print(f'未找到同名文件夹，请更换一个文件夹名称后重试', error_msg=True)
                    return None
                else:
                    message = json_data.get('message', '未知错误')
                    custom_print(f"创建文件夹失败: {message}", error_msg=True)
                    return None
        except Exception as e:
            custom_print(f"创建文件夹失败: {e}", error_msg=True)
            return None

    async def create_date_folder(self) -> Optional[str]:
        """创建日期文件夹，格式为当前日期，如2025-05-14"""
        today = datetime.now().strftime("%Y-%m-%d")
        folder_id = await self.create_dir(pdir_name=today)
        return folder_id

    async def fetch_source(self, source_config: Union[Dict, str]) -> List[Dict]:
        """获取订阅源内容，支持不同类型的订阅源

        Args:
            source_config: 订阅源配置，可以是字符串（兼容旧版本）或字典（新版本）

        Returns:
            List[Dict]: 解析后的条目列表
        """
        # 处理不同的输入格式
        if isinstance(source_config, str):
            # 兼容旧版本，字符串格式默认为RSS类型
            source_url = source_config
            source_type = "rss"
        else:
            # 新版本，字典格式包含类型信息
            source_url = source_config.get("url", "")
            source_type = source_config.get("type", "rss").lower()

        custom_print(f"正在获取订阅源: {source_url}，类型: {source_type}")

        # 根据类型选择不同的处理方式
        if source_type == "rss":
            return await self.fetch_rss_feed(source_url)
        elif source_type == "web":
            return await self.fetch_web_content(source_url)
        else:
            custom_print(f"不支持的订阅源类型: {source_type}，尝试作为RSS处理", error_msg=True)
            return await self.fetch_rss_feed(source_url)

    async def fetch_web_content(self, url: str) -> List[Dict]:
        """获取网页内容，并提取有用信息

        Args:
            url: 网页URL

        Returns:
            List[Dict]: 解析后的条目列表
        """
        custom_print(f"正在获取网页内容: {url}")

        # 使用Playwright获取网页内容
        return await self.fetch_rss_with_playwright(url)

    async def fetch_rss_feed(self, rss_url: str) -> List[Dict]:
        """获取RSS源内容，默认使用代理"""
        custom_print(f"正在获取RSS源: {rss_url}")

        # 将rsshub://格式转换为实际URL
        if rss_url.startswith("rsshub://"):
            # 去掉rsshub://前缀
            path = rss_url[9:]
            # 转换为实际的RSSHub URL
            actual_url = f"https://rsshub.app/{path}"
        else:
            actual_url = rss_url

        # 检查是否是需要使用Playwright的URL
        need_playwright = False
        if "linux.do" in actual_url:
            need_playwright = True
            custom_print(f"检测到需要使用Playwright的URL: {actual_url}")

        if need_playwright:
            return await self.fetch_rss_with_playwright(actual_url)
        else:
            try:
                # RSS获取默认使用代理
                async with await self.get_client(force_proxy=True) as client:
                    timeout = httpx.Timeout(60.0, connect=60.0)
                    response = await client.get(actual_url, timeout=timeout)
                    if response.status_code == 200:
                        # 使用feedparser解析RSS内容
                        feed = feedparser.parse(response.text)
                    else:
                        custom_print(f"获取RSS源失败，状态码: {response.status_code}，尝试使用Playwright", error_msg=True)
                        return await self.fetch_rss_with_playwright(actual_url)

                if feed.bozo:  # 解析出错
                    custom_print(f"RSS源解析出错: {feed.bozo_exception}，尝试使用Playwright", error_msg=True)
                    return await self.fetch_rss_with_playwright(actual_url)

                entries = feed.entries
                custom_print(f"获取到 {len(entries)} 条RSS条目")
                return entries
            except Exception as e:
                custom_print(f"获取RSS源失败: {e}，尝试使用Playwright", error_msg=True)
                return await self.fetch_rss_with_playwright(actual_url)

    async def fetch_rss_with_playwright(self, url: str) -> List[Dict]:
        """使用Playwright获取RSS源内容"""
        custom_print(f"使用Playwright获取RSS源: {url}")

        try:
            async with async_playwright() as p:
                # 使用随机UA
                user_agent = self.get_random_ua()
                custom_print(f"使用随机UA: {user_agent}")

                # 创建浏览器实例
                browser = await p.chromium.launch(headless=True)

                # 创建上下文
                context = await browser.new_context(
                    user_agent=user_agent,
                    viewport={"width": 1920, "height": 1080},
                )

                # 创建页面
                page = await context.new_page()

                # 设置超时
                page.set_default_timeout(60000)

                # 访问URL
                await page.goto(url, wait_until="networkidle")

                # 等待页面加载完成
                await page.wait_for_load_state("networkidle")

                # 获取页面内容
                content = await page.content()

                # 关闭浏览器
                await browser.close()

                # 保存内容到文件，用于调试
                with open('playwright_content.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                custom_print("已保存内容到 playwright_content.html 文件")

                # 特殊处理 linux.do 网站
                if "linux.do" in url:
                    custom_print("检测到 linux.do 网站，使用专用解析器")
                    # 检查内容是否是XML/RSS格式
                    if '<?xml version=' in content and '<rss version=' in content:
                        custom_print("检测到RSS格式内容，使用RSS解析器")
                        return self.parse_linux_do_rss(content)
                    else:
                        custom_print("未检测到RSS格式内容，使用HTML解析器")
                        return await self.parse_linux_do_content(content, url)

                # 检查内容是否是XML/RSS格式
                if '<?xml version=' in content or '<rss version=' in content or '<feed' in content:
                    custom_print("检测到RSS/XML格式内容，使用RSS解析器")

                    # 使用feedparser解析RSS内容
                    feed = feedparser.parse(content)

                    if feed.bozo:  # 解析出错
                        custom_print(f"Playwright获取的RSS内容解析出错: {feed.bozo_exception}", error_msg=True)
                        # 尝试使用自定义解析
                        return await self.parse_html_content(content, url)

                    entries = feed.entries
                    custom_print(f"Playwright获取到 {len(entries)} 条RSS条目")
                    return entries
                else:
                    # 非RSS格式，使用HTML解析
                    custom_print("未检测到RSS格式内容，使用HTML解析器")
                    # 尝试使用自定义解析
                    return await self.parse_html_content(content, url)

        except Exception as e:
            custom_print(f"Playwright获取RSS源失败: {e}", error_msg=True)
            return []

    async def parse_linux_do_content(self, content: str, url: str) -> List[Dict]:
        """解析 linux.do 网站的内容"""
        custom_print(f"使用自定义解析器解析 linux.do 内容")

        try:
            # 保存HTML内容到文件，用于调试
            with open('linux_do_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            custom_print("已保存HTML内容到 linux_do_content.html 文件")

            # 导入所需模块
            import re
            from bs4 import BeautifulSoup

            # 检查内容是否是XML/RSS格式
            if '<?xml version=' in content and '<rss version=' in content:
                custom_print("检测到RSS格式内容，尝试直接解析")
                return self.parse_linux_do_rss(content)

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, 'html.parser')

            # 尝试查找夸克网盘链接
            quark_links = []
            quark_pattern = r'https?://pan\.quark\.cn/s/[a-zA-Z0-9]+'
            quark_links.extend(re.findall(quark_pattern, content))

            # 查找所有链接
            for a_tag in soup.find_all('a'):
                href = a_tag.get('href', '')
                if 'pan.quark.cn/s/' in href:
                    quark_links.append(href)

            # 从口令中提取
            code_pattern = r'/~[a-zA-Z0-9]+~:/'
            quark_codes = re.findall(code_pattern, content)

            # 如果找到夸克网盘链接或口令，直接返回
            if quark_links or quark_codes:
                custom_print(f"从HTML内容中直接提取到 {len(quark_links)} 个夸克网盘链接和 {len(quark_codes)} 个口令")
                # 去重
                quark_links = list(set(quark_links))

                # 创建一个条目
                entry = {
                    'title': "Linux.do 影视资源",
                    'link': url,
                    'summary': "从Linux.do提取的影视资源",
                    'description': "从Linux.do提取的影视资源",
                    'quark_links': quark_links,
                    'quark_codes': quark_codes
                }
                return [entry]

            # 尝试多种选择器
            selectors = [
                'article.topic-list-item',
                'div.topic-list-item',
                'tr.topic-list-item',
                'div.topic-list tr',
                'table.topic-list tr',
                'div.topic-list tbody tr',
                'div.topic-list-item',
                'div.topic',
                'div.post',
                'div.topic-body',
                'div.container list-container',
                'div.list-container',
                'div.container-list',
                'div.topic-list',
                'table.topic-list'
            ]

            articles = []
            for selector in selectors:
                found = soup.select(selector)
                if found:
                    custom_print(f"使用选择器 '{selector}' 找到 {len(found)} 个元素")
                    articles = found
                    break

            # 如果没有找到文章，尝试查找所有链接
            if not articles:
                custom_print("未找到文章条目，尝试提取所有链接")
                # 查找所有可能包含影视内容的链接
                links = soup.find_all('a')

                entries = []
                for link in links:
                    href = link.get('href')
                    text = link.text.strip()

                    # 检查链接文本是否包含影视相关关键词
                    keywords = ['电影', '影视', '视频', '电视', '剧集', '动漫', '综艺', 'movie', 'film', 'video', 'tv', 'show', 'anime']
                    if href and text and any(keyword in text.lower() for keyword in keywords):
                        if not href.startswith('http'):
                            # 相对链接转绝对链接
                            base_url = '/'.join(url.split('/')[:3])  # 获取基础URL (https://linux.do)
                            href = f"{base_url}{href}"

                        # 创建一个类似RSS条目的字典
                        entry = {
                            'title': text,
                            'link': href,
                            'summary': text,  # 使用标题作为摘要
                            'description': text,  # 使用标题作为描述
                        }
                        entries.append(entry)

                custom_print(f"从 linux.do 提取到 {len(entries)} 条影视相关链接")
                return entries

            # 处理找到的文章
            entries = []
            for article in articles:
                try:
                    # 尝试多种方式提取标题和链接
                    title_elem = None
                    link = None

                    # 尝试方式1：查找 a.title
                    title_elem = article.select_one('a.title')
                    if title_elem:
                        title = title_elem.text.strip()
                        link = title_elem.get('href')

                    # 尝试方式2：查找 span.link-top-line a
                    if not title_elem:
                        title_elem = article.select_one('span.link-top-line a')
                        if title_elem:
                            title = title_elem.text.strip()
                            link = title_elem.get('href')

                    # 尝试方式3：查找 td.main-link a
                    if not title_elem:
                        title_elem = article.select_one('td.main-link a')
                        if title_elem:
                            title = title_elem.text.strip()
                            link = title_elem.get('href')

                    # 尝试方式4：查找任何 a 标签
                    if not title_elem:
                        title_elem = article.find('a')
                        if title_elem:
                            title = title_elem.text.strip()
                            link = title_elem.get('href')

                    # 如果找到了标题和链接
                    if title_elem and link:
                        if not link.startswith('http'):
                            # 相对链接转绝对链接
                            base_url = '/'.join(url.split('/')[:3])  # 获取基础URL (https://linux.do)
                            link = f"{base_url}{link}"

                        # 检查是否包含影视相关关键词
                        keywords = ['电影', '影视', '视频', '电视', '剧集', '动漫', '综艺', 'movie', 'film', 'video', 'tv', 'show', 'anime']
                        if any(keyword in title.lower() for keyword in keywords):
                            # 创建一个类似RSS条目的字典
                            entry = {
                                'title': title,
                                'link': link,
                                'summary': title,  # 使用标题作为摘要
                                'description': title,  # 使用标题作为描述
                            }
                            entries.append(entry)
                except Exception as e:
                    custom_print(f"解析文章条目时出错: {e}", error_msg=True)
                    continue

            custom_print(f"从 linux.do 解析到 {len(entries)} 条影视相关条目")

            # 如果需要获取更多内容，可以访问每个文章链接
            if entries and self.rss_config.get("fetch_article_content", False):
                custom_print("获取文章详细内容...")
                for i, entry in enumerate(entries):
                    try:
                        # 访问文章页面获取详细内容
                        async with async_playwright() as p:
                            browser = await p.chromium.launch(headless=True)
                            context = await browser.new_context(user_agent=self.get_random_ua())
                            page = await context.new_page()

                            await page.goto(entry['link'], wait_until="networkidle")
                            await page.wait_for_load_state("networkidle")

                            article_content = await page.content()
                            await browser.close()

                            # 解析文章内容
                            article_soup = BeautifulSoup(article_content, 'html.parser')
                            content_elem = article_soup.select_one('div.post') or article_soup.select_one('div.topic-body') or article_soup.select_one('div.post-content')
                            if content_elem:
                                entry['content'] = str(content_elem)
                                entry['summary'] = content_elem.text[:200] + '...'  # 使用内容前200个字符作为摘要

                                # 查找夸克网盘链接
                                quark_links = re.findall(quark_pattern, str(content_elem))
                                if quark_links:
                                    entry['quark_links'] = quark_links
                                    custom_print(f"从文章 '{entry['title']}' 中提取到 {len(quark_links)} 个夸克网盘链接")
                    except Exception as e:
                        custom_print(f"获取文章 {i+1}/{len(entries)} 详细内容时出错: {e}", error_msg=True)

            return entries

        except Exception as e:
            custom_print(f"解析 linux.do 内容失败: {e}", error_msg=True)
            return []

    def parse_linux_do_rss(self, content: str) -> List[Dict]:
        """直接解析 linux.do 的 RSS 内容"""
        try:
            from bs4 import BeautifulSoup
            import re

            # 保存内容到文件，用于调试
            with open('linux_do_rss.xml', 'w', encoding='utf-8') as f:
                f.write(content)
            custom_print("已保存RSS内容到 linux_do_rss.xml 文件")

            # 使用BeautifulSoup解析XML
            soup = BeautifulSoup(content, 'xml')

            # 查找所有item元素
            items = soup.find_all('item')
            custom_print(f"从RSS中找到 {len(items)} 个条目")

            # 如果没有找到item元素，尝试使用lxml解析
            if not items:
                try:
                    custom_print("使用lxml解析器重新解析RSS内容")
                    soup = BeautifulSoup(content, 'lxml-xml')
                    items = soup.find_all('item')
                    custom_print(f"使用lxml解析器找到 {len(items)} 个条目")
                except:
                    custom_print("lxml解析器解析失败")

            # 如果仍然没有找到item元素，尝试使用html.parser解析
            if not items:
                try:
                    custom_print("使用html.parser解析器重新解析RSS内容")
                    soup = BeautifulSoup(content, 'html.parser')
                    items = soup.find_all('item')
                    custom_print(f"使用html.parser解析器找到 {len(items)} 个条目")
                except:
                    custom_print("html.parser解析器解析失败")

            # 如果仍然没有找到item元素，尝试使用正则表达式提取
            if not items:
                custom_print("使用正则表达式提取RSS条目")
                import re
                item_pattern = r'<item>(.*?)</item>'
                items_text = re.findall(item_pattern, content, re.DOTALL)
                custom_print(f"使用正则表达式找到 {len(items_text)} 个条目")

                # 创建虚拟的item元素
                items = []
                for item_text in items_text:
                    # 提取标题
                    title_match = re.search(r'<title>(.*?)</title>', item_text, re.DOTALL)
                    title = title_match.group(1) if title_match else "无标题"

                    # 提取链接
                    link_match = re.search(r'<link>(.*?)</link>', item_text, re.DOTALL)
                    link = link_match.group(1) if link_match else ""

                    # 提取描述
                    description_match = re.search(r'<description>(.*?)</description>', item_text, re.DOTALL)
                    description = description_match.group(1) if description_match else ""

                    # 创建虚拟的item元素
                    item = {
                        'title': title,
                        'link': link,
                        'description': description
                    }
                    items.append(item)

            entries = []
            for item in items:
                try:
                    # 提取标题和链接
                    if isinstance(item, dict):
                        # 使用正则表达式提取的item
                        title = item.get('title', "无标题")
                        link = item.get('link', "")
                        description = item.get('description', "")
                    else:
                        # 使用BeautifulSoup提取的item
                        title_elem = item.find('title')
                        link_elem = item.find('link')
                        description_elem = item.find('description')

                        title = title_elem.text.strip() if title_elem else "无标题"
                        link = link_elem.text.strip() if link_elem else ""
                        description = description_elem.text if description_elem else ""

                    # 检查是否包含夸克网盘链接
                    quark_links = []

                    # 使用正则表达式查找夸克网盘链接
                    quark_pattern = r'https?://pan\.quark\.cn/s/[a-zA-Z0-9]+'

                    # 从描述中提取链接
                    if description:
                        # 直接从CDATA中提取
                        quark_links.extend(re.findall(quark_pattern, str(description)))

                        # 尝试解析CDATA内容
                        try:
                            desc_soup = BeautifulSoup(description, 'html.parser')
                            # 查找所有链接
                            for a_tag in desc_soup.find_all('a'):
                                href = a_tag.get('href', '')
                                if 'pan.quark.cn/s/' in href:
                                    quark_links.append(href)
                        except:
                            pass

                    # 从口令中提取
                    code_pattern = r'/~[a-zA-Z0-9]+~:/'
                    quark_codes = re.findall(code_pattern, str(description))

                    # 去重
                    quark_links = list(set(quark_links))

                    # 创建条目
                    entry = {
                        'title': title,
                        'link': link,
                        'summary': str(description),
                        'description': str(description),
                        'quark_links': quark_links,
                        'quark_codes': quark_codes
                    }

                    # 只有包含夸克网盘链接或口令的条目才添加
                    if quark_links or quark_codes:
                        custom_print(f"从RSS条目 '{title}' 中提取到 {len(quark_links)} 个夸克网盘链接和 {len(quark_codes)} 个口令")
                        entries.append(entry)
                    else:
                        # 检查标题是否包含影视相关关键词
                        keywords = ['电影', '影视', '视频', '电视', '剧集', '动漫', '综艺', 'movie', 'film', 'video', 'tv', 'show', 'anime']
                        if any(keyword in title.lower() for keyword in keywords):
                            custom_print(f"从RSS条目 '{title}' 中未找到夸克网盘链接，但标题包含影视关键词")
                            entries.append(entry)
                except Exception as e:
                    custom_print(f"解析RSS条目时出错: {e}", error_msg=True)
                    continue

            custom_print(f"从RSS中解析到 {len(entries)} 条条目")
            return entries

        except Exception as e:
            custom_print(f"解析RSS内容失败: {e}", error_msg=True)
            return []

    async def parse_html_content(self, content: str, url: str) -> List[Dict]:
        """通用HTML内容解析，尝试提取有用信息"""
        custom_print(f"尝试从HTML内容中提取信息: {url}")

        try:
            from bs4 import BeautifulSoup

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(content, 'html.parser')

            # 尝试查找所有可能的文章条目
            articles = []

            # 尝试多种选择器
            selectors = [
                'article', '.post', '.entry', '.item', '.article',
                'div.post', 'div.entry', 'div.item', 'div.article',
                'li.post', 'li.entry', 'li.item', 'li.article'
            ]

            for selector in selectors:
                found = soup.select(selector)
                if found:
                    articles.extend(found)
                    break

            # 如果没有找到文章，尝试查找所有链接
            if not articles:
                custom_print("未找到文章条目，尝试提取所有链接")
                links = soup.find_all('a')

                entries = []
                for link in links:
                    href = link.get('href')
                    text = link.text.strip()

                    if href and text and len(text) > 5:  # 忽略太短的文本
                        if not href.startswith('http'):
                            # 相对链接转绝对链接
                            base_url = '/'.join(url.split('/')[:3])
                            href = f"{base_url}{href}"

                        entry = {
                            'title': text,
                            'link': href,
                            'summary': text,
                            'description': text,
                        }
                        entries.append(entry)

                custom_print(f"从HTML中提取到 {len(entries)} 条链接")
                return entries

            # 处理找到的文章
            entries = []
            for article in articles:
                try:
                    # 尝试提取标题和链接
                    title_elem = article.find('h1') or article.find('h2') or article.find('h3') or article.find('a')
                    if title_elem:
                        title = title_elem.text.strip()

                        # 尝试获取链接
                        link = None
                        if title_elem.name == 'a':
                            link = title_elem.get('href')
                        else:
                            link_elem = title_elem.find('a')
                            if link_elem:
                                link = link_elem.get('href')

                        if link and not link.startswith('http'):
                            # 相对链接转绝对链接
                            base_url = '/'.join(url.split('/')[:3])
                            link = f"{base_url}{link}"

                        # 尝试提取内容
                        content_elem = article.find('div', class_='content') or article.find('div', class_='entry-content')
                        content = content_elem.text.strip() if content_elem else ""

                        entry = {
                            'title': title,
                            'link': link or url,
                            'summary': content[:200] + '...' if content else title,
                            'description': content or title,
                            'content': content or title,
                        }
                        entries.append(entry)
                except Exception as e:
                    custom_print(f"解析文章时出错: {e}", error_msg=True)
                    continue

            custom_print(f"从HTML中提取到 {len(entries)} 条条目")
            return entries

        except Exception as e:
            custom_print(f"解析HTML内容失败: {e}", error_msg=True)
            return []

    async def process_rss_entries(self, entries: List[Dict], folder_id: str) -> int:
        """处理RSS条目，提取并转存夸克网盘链接"""
        saved_count = 0
        transfer_count = 0
        retry_count = 0
        total_links = 0

        if not entries:
            custom_print("没有RSS条目需要处理")
            return saved_count

        custom_print(f"开始处理 {len(entries)} 条RSS条目")

        # 遍历每个RSS条目
        for entry in entries:
            title = entry.get('title', '无标题')
            content = entry.get('summary', '') or entry.get('description', '') or entry.get('content', '')

            # 提取夸克网盘链接
            links = []

            # 检查是否有预先提取的夸克链接（从RSS直接解析的）
            if 'quark_links' in entry and entry['quark_links']:
                links = entry['quark_links']
                custom_print(f"从RSS条目中直接获取到 {len(links)} 个夸克网盘链接")
            else:
                # 从内容中提取链接
                links = self.extract_quark_links(content)

            # 更新总链接数
            total_links += len(links)

            if not links:
                continue

            # 处理每个链接
            for link in links:
                # 检查链接是否已成功转存
                if self.is_link_saved(link, check_transfer=True):
                    custom_print(f"链接已成功转存，跳过: {link}")
                    continue

                # 获取链接状态
                link_status = self.get_link_status(link)

                # 如果链接存在但未成功转存
                if link_status:
                    # 检查重试次数，如果超过3次则跳过
                    if link_status.get("retry_count", 0) >= 3:
                        custom_print(f"链接转存失败次数过多，跳过: {link}", error_msg=True)
                        continue

                    custom_print(f"尝试重新转存链接: {link}, 标题: {title}")
                    retry_count += 1
                else:
                    custom_print(f"发现新链接: {link}, 标题: {title}")
                    # 先保存链接到数据库，状态为未转存
                    self.save_link_to_db(link, title, folder_id, self.dir_name)
                    saved_count += 1

                # 尝试转存链接
                try:
                    # 转存链接
                    transfer_result = await self.run(link, folder_id)

                    # 检查转存结果
                    if transfer_result:
                        # 检查transfer_result是否是字典类型
                        if isinstance(transfer_result, dict) and transfer_result.get("success"):
                            # 更新数据库中的转存状态为成功
                            self.save_link_to_db(link, title, folder_id, self.dir_name,
                                               transfer_status=1)

                            # 查询该链接的RSS源信息
                            conn = sqlite3.connect('data/quark_links.db')
                            cursor = conn.cursor()
                            cursor.execute(
                                "SELECT source_name, source_url FROM rss_links WHERE share_link = ? AND source_name != '' AND source_url != '' LIMIT 1",
                                (link,)
                            )
                            rss_source = cursor.fetchone()
                            conn.close()

                            # 更新RSS链接记录的保存状态
                            if rss_source:
                                source_name, source_url = rss_source
                                self.save_rss_link_record(source_name, source_url, link, is_saved=1)
                            else:
                                # 如果找不到源信息，使用条目标题作为源名称
                                entry_source = entry.get('feed_title', '') or entry.get('feed', {}).get('title', '')
                                entry_url = entry.get('feed_link', '') or entry.get('feed', {}).get('link', '')

                                if entry_source and entry_url:
                                    self.save_rss_link_record(entry_source, entry_url, link, is_saved=1)
                                else:
                                    # 如果仍然没有源信息，使用默认值
                                    self.save_rss_link_record("未知来源", "未知URL", link, is_saved=1)

                            transfer_count += 1
                            custom_print(f"链接转存成功: {link}")
                        elif isinstance(transfer_result, bool) and transfer_result:
                            # 如果transfer_result是布尔值True，也视为成功
                            self.save_link_to_db(link, title, folder_id, self.dir_name,
                                               transfer_status=1)

                            # 查询该链接的RSS源信息
                            conn = sqlite3.connect('data/quark_links.db')
                            cursor = conn.cursor()
                            cursor.execute(
                                "SELECT source_name, source_url FROM rss_links WHERE share_link = ? AND source_name != '' AND source_url != '' LIMIT 1",
                                (link,)
                            )
                            rss_source = cursor.fetchone()
                            conn.close()

                            # 更新RSS链接记录的保存状态
                            if rss_source:
                                source_name, source_url = rss_source
                                self.save_rss_link_record(source_name, source_url, link, is_saved=1)
                            else:
                                # 如果找不到源信息，使用条目标题作为源名称
                                entry_source = entry.get('feed_title', '') or entry.get('feed', {}).get('title', '')
                                entry_url = entry.get('feed_link', '') or entry.get('feed', {}).get('link', '')

                                if entry_source and entry_url:
                                    self.save_rss_link_record(entry_source, entry_url, link, is_saved=1)
                                else:
                                    # 如果仍然没有源信息，使用默认值
                                    self.save_rss_link_record("未知来源", "未知URL", link, is_saved=1)

                            transfer_count += 1
                            custom_print(f"链接转存成功: {link}")
                        else:
                            # 更新数据库中的转存状态为失败
                            self.save_link_to_db(link, title, folder_id, self.dir_name,
                                               transfer_status=2,
                                               error_msg=f"转存失败，可能是网盘中已存在该文件，结果类型: {type(transfer_result)}")
                            custom_print(f"链接转存失败: {link}")
                    else:
                        # 更新数据库中的转存状态为失败
                        self.save_link_to_db(link, title, folder_id, self.dir_name,
                                           transfer_status=2,
                                           error_msg="转存失败，可能是网盘中已存在该文件")
                        custom_print(f"链接转存失败: {link}")
                except Exception as e:
                    error_msg = str(e)
                    custom_print(f"转存链接失败: {error_msg}", error_msg=True)

                    # 检查是否是永久性错误（如用户等级限制）
                    permanent_error = False
                    if hasattr(e, 'permanent_error'):
                        permanent_error = getattr(e, 'permanent_error', False)
                    elif '单次转存文件个数超出用户等级限制' in error_msg:
                        permanent_error = True

                    # 更新数据库中的转存状态
                    if permanent_error:
                        # 对于永久性错误，设置重试次数为最大值，避免再次尝试
                        self.save_link_to_db(link, title, folder_id, self.dir_name,
                                           transfer_status=2,
                                           error_msg=f"永久性错误: {error_msg}")
                        # 设置重试次数为最大值
                        conn = sqlite3.connect('data/quark_links.db')
                        cursor = conn.cursor()
                        cursor.execute("UPDATE saved_links SET retry_count = 999 WHERE url = ?", (link,))
                        conn.commit()
                        conn.close()
                        custom_print(f"链接 {link} 因永久性错误被标记为不再重试", error_msg=True)
                    else:
                        # 对于临时性错误，正常更新状态
                        self.save_link_to_db(link, title, folder_id, self.dir_name,
                                           transfer_status=2,
                                           error_msg=error_msg)
                        retry_count += 1

                    custom_print(f"链接转存失败: {link}")

        custom_print(f"处理完成: 发现 {total_links} 个链接，新增 {saved_count} 个，成功转存 {transfer_count} 个，重试 {retry_count} 个")
        return transfer_count

    async def auto_run(self) -> None:
        """自动运行，从订阅源获取链接并转存"""
        custom_print("=" * 50)
        custom_print("开始自动运行...")
        custom_print("=" * 50)

        # 重新加载最新的RSS配置
        custom_print("正在重新加载RSS配置...")
        self.rss_config = self.load_rss_config()
        custom_print("RSS配置加载完成")

        # 确保资源提交管理器已初始化，并包含步游兔和Sroad提交器
        resource_submit_config = self.rss_config.get("resource_submit", {})

        # 确保submitters列表包含buyutu和sroad
        if "submitters" in resource_submit_config:
            if "buyutu" not in resource_submit_config["submitters"]:
                resource_submit_config["submitters"].append("buyutu")
                custom_print("已添加步游兔提交器到配置")
            if "sroad" not in resource_submit_config["submitters"]:
                resource_submit_config["submitters"].append("sroad")
                custom_print("已添加Sroad提交器到配置")

        # 确保buyutu配置存在
        if "buyutu" not in resource_submit_config:
            resource_submit_config["buyutu"] = {
                "enabled": True,
                "api_url": "https://www.buyutu.com/sub",
                "batch_size": 10,
                "use_proxy": False,
                "retry_count": 3,
                "delay": {
                    "min": 1.0,
                    "max": 3.0
                },
                "random_ua": True
            }
            custom_print("已添加步游兔默认配置")

        # 确保sroad配置存在
        if "sroad" not in resource_submit_config:
            resource_submit_config["sroad"] = {
                "enabled": True,
                "api_url": "https://hk10g.sroad.win/sub",
                "batch_size": 10,
                "use_proxy": False,
                "retry_count": 3,
                "delay": {
                    "min": 1.0,
                    "max": 3.0
                },
                "random_ua": True
            }
            custom_print("已添加Sroad默认配置")

        # 重新初始化资源提交管理器
        self.resource_submit_manager = ResourceSubmitManager(resource_submit_config)
        custom_print("资源提交管理器已更新，包含步游兔、Sroad和马克耶网盘提交器")

        # 确保share_records表中存在提交相关的字段
        import quark_resource_submit
        quark_resource_submit.ensure_submit_fields_exist()

        # 初始化新的提交器并检查提交状态
        quark_resource_submit.initialize_new_submitter("buyutu")
        quark_resource_submit.initialize_new_submitter("sroad")
        quark_resource_submit.initialize_new_submitter("macyeah")

        # 创建日期文件夹
        folder_id = await self.create_date_folder()
        if not folder_id:
            custom_print("创建日期文件夹失败，使用当前文件夹", error_msg=True)
            folder_id = self.pdir_id

        # 保存日期文件夹ID，用于后续自动分享
        today_folder_id = folder_id
        today_folder_name = datetime.now().strftime("%Y-%m-%d")

        # 获取订阅源列表
        sources = self.rss_config.get("sources", [])

        # 兼容旧版本，如果没有sources字段，则使用rss_sources字段
        if not sources:
            rss_sources = self.rss_config.get("rss_sources", [])
            if not rss_sources:
                custom_print("未配置订阅源，请在config/rss_config.json中配置", error_msg=True)
                return

            # 将旧版本的rss_sources转换为新版本的sources格式
            sources = [{"url": url, "type": "rss"} for url in rss_sources]
            custom_print("使用旧版本的RSS源配置")

        # 统计信息
        total_saved = 0
        total_entries = 0
        total_links = 0
        total_sources = len(sources)

        custom_print(f"共有 {total_sources} 个订阅源需要处理")

        # 处理每个订阅源
        for source_index, source_config in enumerate(sources, 1):
            # 获取订阅源URL和类型
            if isinstance(source_config, str):
                source_url = source_config
                source_type = "rss"
            else:
                source_url = source_config.get("url", "")
                source_type = source_config.get("type", "rss")

            custom_print(f"正在处理订阅源 ({source_index}/{total_sources}): {source_url}，类型: {source_type}")

            # 获取订阅源条目
            entries = await self.fetch_source(source_config)
            if entries:
                total_entries += len(entries)

                # 统计链接数量
                all_links = []
                for entry in entries:
                    # 检查是否有预先提取的夸克链接
                    if 'quark_links' in entry and entry['quark_links']:
                        links = entry['quark_links']
                        all_links.extend(links)

                        # 记录RSS链接
                        source_name = entry.get('title', '无标题')
                        for link in links:
                            self.save_rss_link_record(source_name, source_url, link)
                    else:
                        # 从内容中提取链接
                        content = entry.get('summary', '') or entry.get('description', '') or entry.get('content', '')
                        links = self.extract_quark_links(content)
                        all_links.extend(links)

                        # 记录RSS链接
                        source_name = entry.get('title', '无标题')
                        for link in links:
                            self.save_rss_link_record(source_name, source_url, link)

                # 去重
                unique_links = list(set(all_links))
                total_links += len(unique_links)

                # 处理链接
                saved_count = await self.process_rss_entries(entries, folder_id)
                total_saved += saved_count

                custom_print(f"订阅源 {source_url} 处理完成: 发现 {len(unique_links)} 个链接，成功转存 {saved_count} 个")
            else:
                custom_print(f"订阅源 {source_url} 未获取到内容")

        # 显示详细统计信息
        custom_print("=" * 50)
        custom_print("自动运行统计信息:")
        custom_print(f"- 处理订阅源: {total_sources} 个")
        custom_print(f"- 获取条目: {total_entries} 条")
        custom_print(f"- 发现夸克网盘链接: {total_links} 个")
        custom_print(f"- 成功转存链接: {total_saved} 个")
        custom_print("=" * 50)

        # 检查是否需要自动分享
        auto_share = self.rss_config.get("auto_share", False)
        custom_print(f"自动分享功能状态: {'已启用' if auto_share else '未启用'}")

        if auto_share:
            custom_print("=" * 50)
            custom_print("开始自动分享...")
            custom_print("=" * 50)

            # 获取分享设置
            share_settings = self.rss_config.get("share_settings", {})
            url_type = share_settings.get("url_type", 1)  # 默认为公开分享
            expired_type = share_settings.get("expired_type", 4)  # 默认为永久有效
            password = share_settings.get("password", "")  # 默认无密码

            # 显示分享设置
            url_type_str = "公开分享" if url_type == 1 else "私密分享"
            expired_type_map = {1: "1天", 2: "7天", 3: "30天", 4: "永久"}
            expired_str = expired_type_map.get(expired_type, "未知")
            password_str = "已设置" if password else "未设置"

            custom_print(f"分享设置: 链接类型={url_type_str}, 有效期={expired_str}, 提取码={password_str}")

            # 自动分享日期文件夹下的子文件夹
            await self.auto_share_folders(today_folder_id, today_folder_name, url_type, expired_type, password)

            # 检查是否需要自动提交分享资源
            resource_submit_config = self.rss_config.get("resource_submit", {})
            auto_submit = resource_submit_config.get("auto_submit", False)
            custom_print(f"自动提交分享资源功能状态: {'已启用' if auto_submit else '未启用'}")

            if auto_submit and self.resource_submit_manager:
                custom_print("=" * 50)
                custom_print("开始自动提交分享资源...")
                custom_print("=" * 50)

                # 确保share_records表中存在提交相关的字段
                quark_resource_submit.ensure_submit_fields_exist()

                # 读取分享链接文件
                share_file_path = f'auto_share/{today_folder_name}_share_links.txt'
                share_links = []

                if os.path.exists(share_file_path):
                    try:
                        with open(share_file_path, 'r', encoding='utf-8') as f:
                            share_links = [line.strip() for line in f.readlines() if line.strip()]
                    except Exception as e:
                        custom_print(f"读取分享链接文件失败: {e}", error_msg=True)

                if share_links:
                    custom_print(f"从文件中读取到 {len(share_links)} 个分享链接")

                    # 提交分享链接
                    result = await quark_resource_submit.submit_share_links(self.resource_submit_manager, share_links)

                    if result.get("code") == 0:
                        custom_print(f"自动提交分享资源成功: {result.get('message')}")
                    else:
                        custom_print(f"自动提交分享资源失败: {result.get('message')}", error_msg=True)
                else:
                    custom_print("没有找到可提交的分享链接", error_msg=True)
            elif auto_submit:
                custom_print("资源提交管理器未初始化，无法提交分享资源", error_msg=True)
            else:
                custom_print("自动提交分享资源功能未启用，跳过提交步骤")
        else:
            custom_print("自动分享功能未启用，跳过分享步骤")

        # 检查未转存的链接并进行转存
        custom_print("=" * 50)
        custom_print("开始检查未转存的链接...")
        custom_print("=" * 50)

        # 使用当前文件夹作为转存目标
        await self.check_unsaved_links(folder_id=self.pdir_id)

        # 检查RSS源中未转存的链接并进行转存
        await self.check_unsaved_rss_links(folder_id=self.pdir_id)

        # 检查未分享的文件夹并进行分享
        custom_print("=" * 50)
        custom_print("开始检查未分享的文件夹...")
        custom_print("=" * 50)

        # 使用当前文件夹作为父文件夹
        shared_count = await self.check_unshared_folders(parent_folder_id=self.pdir_id)

        # 如果有新分享的文件夹，并且启用了自动提交，则提交这些新分享的链接
        if shared_count > 0:
            resource_submit_config = self.rss_config.get("resource_submit", {})
            auto_submit = resource_submit_config.get("auto_submit", False)

            if auto_submit and self.resource_submit_manager:
                custom_print("=" * 50)
                custom_print("开始提交新分享的链接...")
                custom_print("=" * 50)

                # 读取最新的分享链接文件
                timestamp = datetime.now().strftime("%Y%m%d")
                share_files = [f for f in os.listdir('auto_share') if f.startswith(f'unshared_folders_{timestamp}')]

                if share_files:
                    # 使用最新的文件
                    latest_file = sorted(share_files)[-1]
                    share_file_path = f'auto_share/{latest_file}'

                    try:
                        with open(share_file_path, 'r', encoding='utf-8') as f:
                            share_links = [line.strip() for line in f.readlines() if line.strip()]

                        if share_links:
                            custom_print(f"从文件 {share_file_path} 中读取到 {len(share_links)} 个分享链接")

                            # 提交分享链接
                            result = await quark_resource_submit.submit_share_links(
                                self.resource_submit_manager, share_links)

                            if result.get("code") == 0:
                                custom_print(f"提交新分享的链接成功: {result.get('message')}")
                            else:
                                custom_print(f"提交新分享的链接失败: {result.get('message')}", error_msg=True)
                        else:
                            custom_print(f"文件 {share_file_path} 中没有找到有效的分享链接")
                    except Exception as e:
                        custom_print(f"读取或提交新分享的链接失败: {e}", error_msg=True)
                else:
                    custom_print("没有找到今天的分享链接文件")

        # 无论是否启用自动分享，都检查未提交的资源并进行提交
        resource_submit_config = self.rss_config.get("resource_submit", {})
        auto_submit = resource_submit_config.get("auto_submit", False)
        custom_print(f"自动提交资源功能状态: {'已启用' if auto_submit else '未启用'}")

        if auto_submit and self.resource_submit_manager:
            custom_print("=" * 50)
            custom_print("开始检查未提交资源并自动提交...")
            custom_print("=" * 50)

            # 确保share_records表中存在提交相关的字段
            quark_resource_submit.ensure_submit_fields_exist()

            # 查询未提交的记录并提交
            limit = resource_submit_config.get("check_limit", 50)  # 默认检查50条记录
            custom_print(f"准备查询最多 {limit} 条未提交的分享记录")

            result = await quark_resource_submit.submit_unsubmitted_links(
                self.resource_submit_manager, limit)

            if result.get("code") == 0:
                custom_print(f"提交未提交的分享记录成功: {result.get('message')}")
            else:
                custom_print(f"提交未提交的分享记录失败: {result.get('message')}", error_msg=True)

    async def auto_share_folders(self, folder_id: str, folder_name: str, url_type: int = 1,
                               expired_type: int = 4, password: str = '') -> None:
        """
        自动分享指定文件夹下的子文件夹

        Args:
            folder_id: 父文件夹ID
            folder_name: 父文件夹名称
            url_type: 分享链接类型，1=公开，2=私密
            expired_type: 过期类型，1=1天，2=7天，3=30天，4=永久
            password: 提取码，仅在url_type=2时有效
        """
        custom_print(f"开始自动分享 {folder_name} 文件夹下的子文件夹")

        # 确保分享记录表存在
        self.ensure_share_records_table()

        # 创建保存分享链接的文件夹
        os.makedirs('auto_share', exist_ok=True)

        # 分享链接保存路径
        share_file_path = f'auto_share/{folder_name}_share_links.txt'

        # 获取文件夹下的子文件夹列表
        file_list_data = await self.get_sorted_file_list(pdir_fid=folder_id)

        # 检查返回的数据结构
        if 'data' not in file_list_data or 'list' not in file_list_data['data']:
            custom_print(f"获取 {folder_name} 文件夹下的子文件夹列表失败", error_msg=True)
            return

        # 过滤出文件夹
        folders = [item for item in file_list_data['data']['list'] if item.get('dir')]

        if not folders:
            custom_print(f"{folder_name} 文件夹下没有子文件夹，无需分享", error_msg=True)
            return

        custom_print(f"找到 {len(folders)} 个子文件夹需要分享")

        # 清空或创建分享链接文件，只包含链接
        open(share_file_path, 'w', encoding='utf-8').close()

        # 分享统计
        total_folders = len(folders)
        success_count = 0
        error_count = 0

        # 获取延迟设置
        share_settings = self.rss_config.get("share_settings", {})
        delay_settings = share_settings.get("delay", {
            "initial": [3.0, 5.0],
            "before_each": [1.0, 3.0],
            "after_each": [2.0, 5.0]
        })

        # 在开始分享前先等待一段时间，避免频繁请求
        initial_delay_range = delay_settings.get("initial", [3.0, 5.0])
        initial_delay = random.uniform(initial_delay_range[0], initial_delay_range[1])
        custom_print(f"准备开始分享，先等待 {initial_delay:.1f} 秒...")
        await asyncio.sleep(initial_delay)

        # 遍历分享每个子文件夹
        for index, folder in enumerate(folders, 1):
            subfolder_name = folder.get('file_name', '未命名文件夹')
            subfolder_id = folder.get('fid', '')

            if not subfolder_id:
                custom_print(f"子文件夹 {subfolder_name} 的ID为空，跳过分享", error_msg=True)
                error_count += 1
                continue

            # 检查文件夹是否已经分享过
            if self.is_folder_shared(subfolder_id):
                custom_print(f"子文件夹 {subfolder_name} 已经分享过，跳过分享")
                # 获取之前的分享链接
                conn = sqlite3.connect('data/quark_links.db')
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT share_url FROM share_records WHERE subfolder_id = ? AND status = 1 AND share_url != '' ORDER BY id DESC LIMIT 1",
                    (subfolder_id,)
                )
                result = cursor.fetchone()
                conn.close()

                if result and result[0]:
                    # 将之前的分享链接添加到文件中
                    with open(share_file_path, 'a', encoding='utf-8') as f:
                        f.write(f"{result[0]}\n")
                    success_count += 1

                continue

            # 每次分享前增加一个随机延迟
            if index > 1:  # 第一个文件夹已经有初始延迟了
                before_each_range = delay_settings.get("before_each", [1.0, 3.0])
                pre_delay = random.uniform(before_each_range[0], before_each_range[1])
                custom_print(f"准备分享下一个文件夹，等待 {pre_delay:.1f} 秒...")
                await asyncio.sleep(pre_delay)

            custom_print(f"正在分享 ({index}/{total_folders}): {subfolder_name}")

            # 尝试分享文件夹
            try:
                # 获取分享任务ID
                max_retries = 2  # 最大重试次数
                retry_count = 0
                task_id = ""

                while retry_count <= max_retries and not task_id:
                    if retry_count > 0:
                        custom_print(f"重试获取分享任务ID ({retry_count}/{max_retries})...")
                        # 重试前等待一段时间
                        retry_delay = random.uniform(3.0, 5.0)
                        await asyncio.sleep(retry_delay)

                    task_id = await self.get_share_task_id(subfolder_id, subfolder_name,
                                                          url_type=url_type,
                                                          expired_type=expired_type,
                                                          password=password)
                    retry_count += 1

                if not task_id:
                    custom_print(f"获取分享任务ID失败: {subfolder_name}", error_msg=True)
                    error_count += 1

                    # 记录错误到数据库
                    self.save_share_record(
                        folder_id=folder_id,
                        folder_name=folder_name,
                        subfolder_id=subfolder_id,
                        subfolder_name=subfolder_name,
                        share_url="",
                        status=0,
                        error_message="获取分享任务ID失败"
                    )

                    continue

                # 获取分享ID
                retry_count = 0
                share_id = ""

                while retry_count <= max_retries and not share_id:
                    if retry_count > 0:
                        custom_print(f"重试获取分享ID ({retry_count}/{max_retries})...")
                        # 重试前等待一段时间
                        retry_delay = random.uniform(3.0, 5.0)
                        await asyncio.sleep(retry_delay)

                    share_id = await self.get_share_id(task_id)
                    retry_count += 1

                if not share_id:
                    custom_print(f"获取分享ID失败: {subfolder_name}", error_msg=True)
                    error_count += 1

                    # 记录错误到数据库
                    self.save_share_record(
                        folder_id=folder_id,
                        folder_name=folder_name,
                        subfolder_id=subfolder_id,
                        subfolder_name=subfolder_name,
                        share_url="",
                        status=0,
                        error_message="获取分享ID失败"
                    )

                    continue

                # 提交分享并获取分享链接
                retry_count = 0
                share_url = ""

                while retry_count <= max_retries and not share_url:
                    if retry_count > 0:
                        custom_print(f"重试获取分享链接 ({retry_count}/{max_retries})...")
                        # 重试前等待一段时间
                        retry_delay = random.uniform(3.0, 5.0)
                        await asyncio.sleep(retry_delay)

                    share_url = await self.submit_share(share_id)
                    retry_count += 1

                if not share_url:
                    custom_print(f"获取分享链接失败: {subfolder_name}", error_msg=True)
                    error_count += 1

                    # 记录错误到数据库
                    self.save_share_record(
                        folder_id=folder_id,
                        folder_name=folder_name,
                        subfolder_id=subfolder_id,
                        subfolder_name=subfolder_name,
                        share_url="",
                        status=0,
                        error_message="获取分享链接失败"
                    )

                    continue

                # 保存分享链接到文件（只保存链接本身）
                with open(share_file_path, 'a', encoding='utf-8') as f:
                    f.write(f"{share_url}\n")

                # 保存分享记录到数据库
                self.save_share_record(
                    folder_id=folder_id,
                    folder_name=folder_name,
                    subfolder_id=subfolder_id,
                    subfolder_name=subfolder_name,
                    share_url=share_url,
                    status=1
                )

                custom_print(f"分享成功: {subfolder_name} | {share_url}")
                success_count += 1

                # 随机暂停一段时间，避免请求过于频繁导致被限制
                after_each_range = delay_settings.get("after_each", [2.0, 5.0])
                delay = random.uniform(after_each_range[0], after_each_range[1])
                custom_print(f"等待 {delay:.1f} 秒后继续下一个分享...")
                await asyncio.sleep(delay)

            except Exception as e:
                custom_print(f"分享 {subfolder_name} 时出错: {e}", error_msg=True)
                error_count += 1

                # 记录错误到数据库
                self.save_share_record(
                    folder_id=folder_id,
                    folder_name=folder_name,
                    subfolder_id=subfolder_id,
                    subfolder_name=subfolder_name,
                    share_url="",
                    status=0,
                    error_message=str(e)
                )

        # 不在文件中添加统计信息，保持文件中只有链接

        custom_print("=" * 50)
        custom_print("自动分享统计信息:")
        custom_print(f"- 总文件夹数: {total_folders}")
        custom_print(f"- 成功分享: {success_count}")
        custom_print(f"- 分享失败: {error_count}")
        custom_print(f"- 分享链接已保存到: {share_file_path}")
        custom_print("=" * 50)

    async def scheduled_auto_run(self) -> None:
        """定时自动运行"""
        try:
            while True:
                # 重新加载配置以获取最新的间隔时间
                self.rss_config = self.load_rss_config()
                interval = self.rss_config.get("auto_run_interval", 3600)  # 默认1小时

                # 运行自动任务（auto_run 方法内部会再次重新加载配置）
                await self.auto_run()

                custom_print(f"等待 {interval} 秒后再次运行...")
                custom_print(f"按 Ctrl+C 可以停止定时运行")

                # 分段等待，每10秒检查一次是否有中断请求
                for _ in range(interval // 10):
                    await asyncio.sleep(10)
                    # 这里可以添加检查中断标志的逻辑

                # 处理剩余的等待时间
                await asyncio.sleep(interval % 10)
        except asyncio.CancelledError:
            custom_print("定时任务被取消")
            raise
        except KeyboardInterrupt:
            custom_print("用户中断，停止定时运行")
            return

    async def run(self, surl: str, folder_id: Union[str, None] = None, download: bool = False) -> bool:
        """
        转存或下载分享链接

        Args:
            surl: 分享链接
            folder_id: 保存的文件夹ID
            download: 是否下载到本地

        Returns:
            bool: 转存是否成功
        """
        self.folder_id = folder_id
        custom_print(f'文件分享链接：{surl}')

        try:
            pwd_id = self.get_pwd_id(surl)
            stoken = await self.get_stoken(pwd_id)
            if not stoken:
                custom_print("获取stoken失败，无法转存", error_msg=True)
                return False

            is_owner, data_list = await self.get_detail(pwd_id, stoken)
            if not data_list:
                custom_print("获取文件详情失败，无法转存", error_msg=True)
                return False

            files_count = 0
            folders_count = 0
            files_list: List[str] = []
            folders_list: List[str] = []
            files_id_list = []
            file_fid_list = []

            total_files_count = len(data_list)
            for data in data_list:
                if data['dir']:
                    folders_count += 1
                    folders_list.append(data["file_name"])
                else:
                    files_count += 1
                    files_list.append(data["file_name"])
                    files_id_list.append((data["fid"], data["file_name"]))

            custom_print(f'转存总数：{total_files_count}，文件数：{files_count}，文件夹数：{folders_count} | 支持嵌套')
            if files_list:
                custom_print(f'文件转存列表：{files_list}')
            if folders_list:
                custom_print(f'文件夹转存列表：{folders_list}')

            fid_list = [i["fid"] for i in data_list]
            share_fid_token_list = [i["share_fid_token"] for i in data_list]

            if not self.folder_id:
                custom_print('保存目录ID不合法，请重新获取，如果无法获取，请输入0作为文件夹ID')
                return False

            if download:
                if is_owner == 0:
                    custom_print(f'下载文件必须是网盘内文件，请先将文件转存至网盘中')
                    return False

                download_success = True
                for i in data_list:
                    if i['dir']:
                        data_list2 = [i]
                        not_dir = False
                        while True:
                            data_list3 = []
                            for i2 in data_list2:
                                custom_print(f'开始下载：{i2["file_name"]} 文件夹中的{i2["include_items"]}个文件')
                                is_owner, file_data_list = await self.get_detail(pwd_id, stoken, pdir_fid=i2['fid'])
                                folder = i["file_name"]
                                fid_list = [i["fid"] for i in file_data_list]
                                download_result = await self.quark_file_download(fid_list, folder=folder)
                                if not download_result:
                                    download_success = False
                                file_fid_list.extend([i for i in file_data_list if not i2['dir']])
                                dir_list = [i for i in file_data_list if i['dir']]
                                if not dir_list:
                                    not_dir = True
                                data_list3.extend(dir_list)
                            data_list2 = data_list3
                            if not data_list2 or not_dir:
                                break

                if len(files_id_list) > 0 or len(file_fid_list) > 0:
                    fid_list = [i[0] for i in files_id_list]
                    file_fid_list.extend(fid_list)
                    download_result = await self.quark_file_download(file_fid_list, folder='.')
                    if not download_result:
                        download_success = False

                return download_success
            else:
                if is_owner == 1:
                    custom_print(f'网盘中已经存在该文件，无需再次转存')
                    return True

                task_id = await self.get_share_save_task_id(pwd_id, stoken, fid_list, share_fid_token_list,
                                                            to_pdir_fid=self.folder_id)
                if not task_id:
                    custom_print("获取任务ID失败，无法转存", error_msg=True)
                    return False

                result = await self.submit_task(task_id)
                if result and isinstance(result, dict) and result.get('message') == 'ok':
                    custom_print("转存成功")
                    return True
                else:
                    # 检查是否是永久性错误
                    if result and isinstance(result, dict) and result.get('permanent_error'):
                        error_msg = result.get('message', '未知错误')
                        custom_print(f"转存失败（永久性错误）: {error_msg}", error_msg=True)
                        # 创建一个自定义异常，包含永久性错误标记
                        class PermanentError(Exception):
                            def __init__(self, message):
                                self.message = message
                                self.permanent_error = True
                                super().__init__(message)

                        # 抛出永久性错误异常
                        raise PermanentError(error_msg)
                    else:
                        custom_print("转存失败", error_msg=True)
                        return False

        except Exception as e:
            # 检查是否是永久性错误
            if hasattr(e, 'permanent_error') and getattr(e, 'permanent_error', False):
                # 重新抛出永久性错误，让上层处理
                raise
            else:
                custom_print(f"转存过程中发生错误: {e}", error_msg=True)
                return False

    async def get_share_save_task_id(self, pwd_id: str, stoken: str, first_ids: List[str], share_fid_tokens: List[str],
                                     to_pdir_fid: str = '0') -> str:
        """
        获取分享保存任务ID

        Args:
            pwd_id: 分享ID
            stoken: 分享token
            first_ids: 文件ID列表
            share_fid_tokens: 分享文件token列表
            to_pdir_fid: 保存的文件夹ID

        Returns:
            str: 任务ID，失败时返回空字符串
        """
        task_url = "https://drive.quark.cn/1/clouddrive/share/sharepage/save"
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "uc_param_str": "",
            "__dt": random.randint(600, 9999),
            "__t": get_timestamp(13),
        }
        data = {"fid_list": first_ids,
                "fid_token_list": share_fid_tokens,
                "to_pdir_fid": to_pdir_fid, "pwd_id": pwd_id,
                "stoken": stoken, "pdir_fid": "0", "scene": "link"}

        try:
            # 直接使用httpx客户端，不使用重试机制
            async with httpx.AsyncClient() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post(task_url, json=data, headers=self.headers, params=params, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取任务ID失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return ""

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取任务ID失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return ""

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取任务ID失败: 服务器返回空响应", error_msg=True)
                    return ""

                # 检查data字段是否存在
                if 'data' not in json_data:
                    message = json_data.get('message', '未知错误')
                    custom_print(f"获取任务ID失败: {message}", error_msg=True)
                    return ""

                # 检查task_id字段是否存在
                if 'task_id' not in json_data['data']:
                    custom_print("获取任务ID失败: 响应中没有task_id字段", error_msg=True)
                    return ""

                task_id = json_data['data']['task_id']
                custom_print(f'获取任务ID：{task_id}')
                return task_id
        except Exception as e:
            custom_print(f"获取任务ID失败: {e}", error_msg=True)
            return ""

    async def download_file(self, download_url: str, save_path: str, headers: dict) -> None:
        async with await self.get_client() as client:
            timeout = httpx.Timeout(60.0, connect=60.0)
            async with client.stream("GET", download_url, headers=headers, timeout=timeout) as response:
                total_size = int(response.headers["content-length"])
                with open(save_path, "wb") as f:
                    with tqdm(total=total_size, unit="B", unit_scale=True,
                              desc=os.path.basename(save_path),
                              ncols=80) as pbar:
                        async for chunk in response.aiter_bytes():
                            f.write(chunk)
                            pbar.update(len(chunk))

    async def quark_file_download(self, fids: List[str], folder: str = '') -> bool:
        """
        下载文件

        Args:
            fids: 文件ID列表
            folder: 保存的文件夹名称

        Returns:
            bool: 下载是否成功
        """
        if not fids:
            custom_print("没有要下载的文件", error_msg=True)
            return False

        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
            '__dt': random.randint(600, 9999),
            '__t': get_timestamp(13),
        }

        data = {
            'fids': fids
        }
        download_api = 'https://drive-pc.quark.cn/1/clouddrive/file/download'

        try:
            async with await self.get_client() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post(download_api, json=data, headers=self.headers, params=params, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"文件下载地址列表获取失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return False

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"文件下载地址列表获取失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return False

                # 检查响应是否有效
                if not json_data:
                    custom_print("文件下载地址列表获取失败: 服务器返回空响应", error_msg=True)
                    return False

                # 检查状态码
                if json_data.get('status') != 200:
                    message = json_data.get('message', '未知错误')
                    custom_print(f"文件下载地址列表获取失败: {message}", error_msg=True)
                    return False

                # 获取数据列表
                data_list = json_data.get('data', [])
                if not data_list:
                    custom_print("文件下载地址列表为空", error_msg=True)
                    return False

                custom_print('文件下载地址列表获取成功')

                # 创建保存文件夹
                save_folder = f'downloads/{folder}' if folder else 'downloads'
                os.makedirs(save_folder, exist_ok=True)

                # 下载文件
                download_success = True
                n = 0
                for i in data_list:
                    n += 1
                    try:
                        filename = i.get("file_name", f"未命名文件_{n}")
                        custom_print(f'开始下载第{n}个文件-{filename}')

                        if "download_url" not in i:
                            custom_print(f"文件 {filename} 没有下载地址", error_msg=True)
                            download_success = False
                            continue

                        download_url = i["download_url"]
                        save_path = os.path.join(save_folder, filename)

                        try:
                            await self.download_file(download_url, save_path, headers=self.headers)
                            custom_print(f'文件 {filename} 下载成功')
                        except Exception as e:
                            custom_print(f'文件 {filename} 下载失败: {e}', error_msg=True)
                            download_success = False
                    except Exception as e:
                        custom_print(f'处理第{n}个文件时出错: {e}', error_msg=True)
                        download_success = False

                return download_success
        except Exception as e:
            custom_print(f"文件下载失败: {e}", error_msg=True)
            return False

    async def submit_task(self, task_id: str, retry: int = 50) -> Dict[str, Any]:
        """
        提交任务并等待完成

        Args:
            task_id: 任务ID
            retry: 最大重试次数

        Returns:
            Dict: 任务结果，成功时返回 {"message": "ok", "data": {...}}
        """
        for i in range(retry):
            # 随机暂停100-50毫秒
            await asyncio.sleep(random.randint(500, 1000) / 1000)
            custom_print(f'第{i + 1}次提交任务')
            submit_url = (f"https://drive-pc.quark.cn/1/clouddrive/task?pr=ucpro&fr=pc&uc_param_str=&task_id={task_id}"
                          f"&retry_index={i}&__dt=21192&__t={get_timestamp(13)}")

            try:
                # 直接使用httpx客户端，不使用重试机制
                async with httpx.AsyncClient() as client:
                    timeout = httpx.Timeout(60.0, connect=60.0)
                    response = await client.get(submit_url, headers=self.headers, timeout=timeout)

                    # 检查响应状态码
                    if response.status_code != 200:
                        custom_print(f"提交任务失败: HTTP状态码 {response.status_code}，尝试重试", error_msg=True)
                        continue

                    # 尝试解析JSON响应
                    try:
                        json_data = response.json()
                    except Exception as e:
                        custom_print(f"提交任务失败: 无法解析JSON响应 - {e}，尝试重试", error_msg=True)
                        continue

                    # 检查响应是否有效
                    if not json_data:
                        custom_print("提交任务失败: 服务器返回空响应，尝试重试", error_msg=True)
                        continue

                    if json_data.get('message') == 'ok':
                        if 'data' in json_data and json_data['data'].get('status') == 2:
                            folder_name = '根目录'
                            if 'save_as' in json_data['data'] and 'to_pdir_name' in json_data['data']['save_as']:
                                folder_name = json_data['data']['save_as']['to_pdir_name']

                            if json_data['data'].get('task_title') == '分享-转存':
                                custom_print(f"结束任务ID：{task_id}")
                                custom_print(f'文件保存位置：{folder_name} 文件夹')
                            return json_data
                    else:
                        # 处理错误情况
                        error_code = json_data.get('code', 0)
                        error_message = json_data.get('message', '未知错误')

                        if error_code == 32003 and 'capacity limit' in error_message:
                            custom_print("转存失败，网盘容量不足！请注意当前已成功保存的个数，避免重复保存", error_msg=True)
                            return {"message": error_message, "code": error_code, "permanent_error": True}
                        elif error_code == 41013:
                            custom_print(f"网盘文件夹不存在，请重新运行按3切换保存目录后重试！", error_msg=True)
                            return {"message": error_message, "code": error_code, "permanent_error": True}
                        # 检测单次转存文件个数超出用户等级限制的错误
                        elif '单次转存文件个数超出用户等级限制' in error_message:
                            custom_print(f"转存失败：单次转存文件个数超出用户等级限制，跳过该链接", error_msg=True)
                            return {"message": error_message, "code": error_code, "permanent_error": True}
                        else:
                            custom_print(f"错误信息：{error_message}，尝试重试", error_msg=True)
                            # 继续重试
            except Exception as e:
                custom_print(f"提交任务失败: {e}，尝试重试", error_msg=True)
                continue

        # 达到最大重试次数后返回错误
        custom_print(f"提交任务失败: 达到最大重试次数 {retry}", error_msg=True)
        return {"message": "达到最大重试次数", "code": -1}


    def init_config(self, _user, _pdir_id, _dir_name):
        try:
            os.makedirs('share', exist_ok=True)
            json_data = read_config(f'{CONFIG_DIR}/config.json', 'json')
            if json_data:
                user = json_data.get('user', 'jack')
                if user != _user:
                    _pdir_id = '0'
                    _dir_name = '根目录'
                    new_config = {'user': _user, 'pdir_id': _pdir_id, 'dir_name': _dir_name}
                    save_config(f'{CONFIG_DIR}/config.json', content=json.dumps(new_config, ensure_ascii=False))
                else:
                    _pdir_id = json_data.get('pdir_id', '0')
                    _dir_name = json_data.get('dir_name', '根目录')
        except (json.decoder.JSONDecodeError, FileNotFoundError):
            new_config = {'user': self.user, 'pdir_id': self.pdir_id, 'dir_name': self.dir_name}
            save_config(f'{CONFIG_DIR}/config.json', content=json.dumps(new_config, ensure_ascii=False))
        return _user, _pdir_id, _dir_name

    async def load_folder_id(self, renew=False) -> Union[tuple, None]:

        self.user = await self.get_user_info()
        self.user, self.pdir_id, self.dir_name = self.init_config(self.user, self.pdir_id, self.dir_name)
        if not renew:
            custom_print(f'用户名：{self.user}')
            custom_print(f'你当前选择的网盘保存目录: {self.dir_name} 文件夹')

        if renew:
            pdir_id = input(f'[{get_datetime()}] 请输入保存位置的文件夹ID(可为空): ')
            if pdir_id == '0':
                self.dir_name = '根目录'
                new_config = {'user': self.user, 'pdir_id': self.pdir_id, 'dir_name': self.dir_name}
                save_config(f'{CONFIG_DIR}/config.json', content=json.dumps(new_config, ensure_ascii=False))

            elif len(pdir_id) < 32:
                file_list_data = await self.get_sorted_file_list()
                if not file_list_data:
                    custom_print('获取文件列表失败，保存目录切换失败', error_msg=True)
                    json_data = read_config(f'{CONFIG_DIR}/config.json', 'json')
                    return json_data['pdir_id'], json_data['dir_name']

                # 过滤出文件夹类型的项目
                fd_list = [{i['fid']: i['file_name']} for i in file_list_data if i.get('dir')]
                if fd_list:
                    table = PrettyTable(['序号', '文件夹ID', '文件夹名称'])
                    for idx, item in enumerate(fd_list, 1):
                        key, value = next(iter(item.items()))
                        table.add_row([idx, key, value])
                    print(table)
                    num = input(f'[{get_datetime()}] 请选择你要保存的位置（输入对应序号）: ')
                    if not num or int(num) > len(fd_list):
                        custom_print('输入序号不存在，保存目录切换失败', error_msg=True)
                        json_data = read_config(f'{CONFIG_DIR}/config.json', 'json')
                        return json_data['pdir_id'], json_data['dir_name']

                    item = fd_list[int(num) - 1]
                    self.pdir_id, self.dir_name = next(iter(item.items()))
                    new_config = {'user': self.user, 'pdir_id': self.pdir_id, 'dir_name': self.dir_name}
                    save_config(f'{CONFIG_DIR}/config.json', content=json.dumps(new_config, ensure_ascii=False))

        return self.pdir_id, self.dir_name

    async def get_share_task_id(self, fid: str, file_name: str, url_type: int = 1, expired_type: int = 2,
                                password: str = '') -> str:

        json_data = {
            "fid_list": [
                fid
            ],
            "title": file_name,

            "url_type": url_type,
            "expired_type": expired_type
        }
        if url_type == 2:
            if password:
                json_data["passcode"] = password
            else:
                json_data["passcode"] = generate_random_code()

        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
        }

        try:
            async with await self.get_client() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post('https://drive-pc.quark.cn/1/clouddrive/share', params=params,
                                         json=json_data, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取分享任务ID失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return ""

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取分享任务ID失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return ""

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取分享任务ID失败: 服务器返回空响应", error_msg=True)
                    return ""

                # 检查data字段是否存在
                if 'data' not in json_data:
                    custom_print(f"获取分享任务ID失败: 响应中没有data字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    return ""

                # 检查task_id字段是否存在
                if 'task_id' not in json_data['data']:
                    custom_print(f"获取分享任务ID失败: 响应中没有task_id字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    # 打印完整响应以便调试
                    custom_print(f"完整响应: {json_data}")
                    return ""

                task_id = json_data['data']['task_id']
                return task_id
        except Exception as e:
            custom_print(f"获取分享任务ID失败: {e}", error_msg=True)
            return ""

    async def get_share_id(self, task_id: str) -> str:
        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
            'task_id': task_id,
            'retry_index': '0',
        }
        try:
            async with await self.get_client() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.get('https://drive-pc.quark.cn/1/clouddrive/task', params=params,
                                        headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取分享ID失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return ""

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"获取分享ID失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return ""

                # 检查响应是否有效
                if not json_data:
                    custom_print("获取分享ID失败: 服务器返回空响应", error_msg=True)
                    return ""

                # 检查data字段是否存在
                if 'data' not in json_data:
                    custom_print(f"获取分享ID失败: 响应中没有data字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    return ""

                # 检查share_id字段是否存在
                if 'share_id' not in json_data['data']:
                    # 打印完整响应以便调试
                    custom_print(f"获取分享ID失败: 响应中没有share_id字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    custom_print(f"完整响应: {json_data}")

                    # 检查是否有其他可能的字段包含分享ID
                    if json_data.get('message') == 'ok' and 'data' in json_data:
                        # 尝试从其他字段获取分享ID
                        if 'share_info' in json_data['data'] and 'share_id' in json_data['data']['share_info']:
                            share_id = json_data['data']['share_info']['share_id']
                            custom_print(f"从share_info字段中找到share_id: {share_id}")
                            return share_id

                        # 尝试从task_result字段获取
                        if 'task_result' in json_data['data'] and 'share_id' in json_data['data']['task_result']:
                            share_id = json_data['data']['task_result']['share_id']
                            custom_print(f"从task_result字段中找到share_id: {share_id}")
                            return share_id

                        # 尝试从save_as字段获取
                        if 'save_as' in json_data['data'] and 'share_id' in json_data['data']['save_as']:
                            share_id = json_data['data']['save_as']['share_id']
                            custom_print(f"从save_as字段中找到share_id: {share_id}")
                            return share_id

                        # 如果status为2（完成），可能是任务已经完成
                        if json_data['data'].get('status') == 2:
                            # 尝试直接使用task_id作为share_id
                            task_id = json_data['data'].get('task_id', '')
                            if task_id:
                                custom_print(f"任务已完成，尝试使用task_id作为share_id: {task_id}")
                                return task_id

                    # 如果所有尝试都失败，返回空字符串
                    return ""

                share_id = json_data['data']['share_id']
                return share_id
        except Exception as e:
            custom_print(f"获取分享ID失败: {e}", error_msg=True)
            return ""

    async def submit_share(self, share_id: str) -> str:
        params = {
            'pr': 'ucpro',
            'fr': 'pc',
            'uc_param_str': '',
        }

        json_data = {
            'share_id': share_id,
        }
        try:
            async with await self.get_client() as client:
                timeout = httpx.Timeout(60.0, connect=60.0)
                response = await client.post('https://drive-pc.quark.cn/1/clouddrive/share/password', params=params,
                                         json=json_data, headers=self.headers, timeout=timeout)

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"提交分享失败: HTTP状态码 {response.status_code}", error_msg=True)
                    return ""

                # 尝试解析JSON响应
                try:
                    json_data = response.json()
                except Exception as e:
                    custom_print(f"提交分享失败: 无法解析JSON响应 - {e}", error_msg=True)
                    return ""

                # 检查响应是否有效
                if not json_data:
                    custom_print("提交分享失败: 服务器返回空响应", error_msg=True)
                    return ""

                # 检查data字段是否存在
                if 'data' not in json_data:
                    custom_print(f"提交分享失败: 响应中没有data字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    return ""

                # 检查share_url字段是否存在
                if 'share_url' not in json_data['data']:
                    custom_print(f"提交分享失败: 响应中没有share_url字段 - {json_data.get('message', '未知错误')}", error_msg=True)
                    # 打印完整响应以便调试
                    custom_print(f"完整响应: {json_data}")

                    # 尝试从其他可能的字段获取分享URL
                    if json_data.get('message') == 'ok' and 'data' in json_data:
                        # 尝试从share_info字段获取
                        if 'share_info' in json_data['data'] and 'share_url' in json_data['data']['share_info']:
                            share_url = json_data['data']['share_info']['share_url']
                            custom_print(f"从share_info字段中找到share_url: {share_url}")

                            # 检查是否有提取码
                            if 'passcode' in json_data['data']['share_info']:
                                share_url = share_url + f"?pwd={json_data['data']['share_info']['passcode']}"

                            return share_url

                        # 尝试从其他可能的字段获取
                        for field in ['share_result', 'task_result', 'result']:
                            if field in json_data['data'] and 'share_url' in json_data['data'][field]:
                                share_url = json_data['data'][field]['share_url']
                                custom_print(f"从{field}字段中找到share_url: {share_url}")

                                # 检查是否有提取码
                                if 'passcode' in json_data['data'][field]:
                                    share_url = share_url + f"?pwd={json_data['data'][field]['passcode']}"

                                return share_url

                    # 如果所有尝试都失败，返回空字符串
                    return ""

                share_url = json_data['data']['share_url']
                if 'passcode' in json_data['data']:
                    share_url = share_url + f"?pwd={json_data['data']['passcode']}"
                return share_url
        except Exception as e:
            custom_print(f"提交分享失败: {e}", error_msg=True)
            return ""

    async def share_run(self, share_url: str, folder_id: Union[str, None] = None, url_type: int = 1,
                        expired_type: int = 2, password: str = '') -> None:
        first_dir = ''
        second_dir = ''
        try:
            self.folder_id = folder_id
            custom_print(f'文件夹网页地址：{share_url}')
            pwd_id = share_url.rsplit('/', maxsplit=1)[1].split('-')[0]

            first_page = 1
            n = 0
            error = 0
            os.makedirs('share', exist_ok=True)
            save_share_path = 'share/share_url.txt'

            safe_copy(save_share_path, 'share/share_url_backup.txt')
            with open(save_share_path, 'w', encoding='utf-8'):
                pass
            while True:
                file_list_data = await self.get_sorted_file_list(pwd_id, page=str(first_page), size='50', fetch_total='1',
                                                            sort='file_type:asc,file_name:asc')
                if not file_list_data:
                    custom_print("获取文件列表失败，退出分享循环", error_msg=True)
                    break

                for i1 in file_list_data:
                    if i1.get('dir'):
                        first_dir = i1.get('file_name', '未知文件夹')
                        second_page = 1
                        while True:
                            # print(f'正在获取{first_dir}第{first_page}页，二级目录第{second_page}页，目前共分享{n}文件')
                            file_list_data2 = await self.get_sorted_file_list(i1.get('fid', ''), page=str(second_page),
                                                                         size='50', fetch_total='1',
                                                                         sort='file_type:asc,file_name:asc')
                            if not file_list_data2:
                                custom_print(f"获取 {first_dir} 的子文件夹列表失败，跳过", error_msg=True)
                                break

                            for i2 in file_list_data2:
                                if i2['dir']:
                                    n += 1
                                    share_success = False
                                    share_error_msg = ''
                                    fid = ''
                                    for i in range(3):
                                        try:
                                            second_dir = i2['file_name']
                                            custom_print(f'{n}.开始分享 {first_dir}/{second_dir} 文件夹')
                                            random_time = random.choice([0.5, 1, 1.5, 2])
                                            await asyncio.sleep(random_time)
                                            # print('获取到文件夹ID：', i2['fid'])
                                            fid = i2['fid']
                                            task_id = await self.get_share_task_id(fid, second_dir, url_type=url_type,
                                                                                   expired_type=expired_type,
                                                                                   password=password)
                                            share_id = await self.get_share_id(task_id)
                                            share_url = await self.submit_share(share_id)
                                            with open(save_share_path, 'a', encoding='utf-8') as f:
                                                content = f'{n} | {first_dir} | {second_dir} | {share_url}'
                                                f.write(content + '\n')
                                                custom_print(f'{n}.分享成功 {first_dir}/{second_dir} 文件夹')
                                                share_success = True
                                                break

                                        except Exception as e:
                                            share_error_msg = e
                                            error += 1

                                    if not share_success:
                                        print('分享失败：', share_error_msg)
                                        save_config('./share/share_error.txt',
                                                    content=f'{error}.{first_dir}/{second_dir} 文件夹\n', mode='a')
                                        save_config('./share/retry.txt',
                                                    content=f'{n} | {first_dir} | {second_dir} | {fid}\n', mode='a')
                            # 简化分页逻辑，只处理第一页数据
                            break

                # 简化分页逻辑，只处理第一页数据
                break
            custom_print(f"总共分享了 {n} 个文件夹，已经保存至 {save_share_path}")

        except Exception as e:
            print('分享失败：', e)
            with open('./share/share_error.txt', 'a', encoding='utf-8') as f:
                f.write(f'{first_dir}/{second_dir} 文件夹')

    async def share_run_retry(self, retry_url: str, url_type: int = 1, expired_type: int = 2, password: str = ''):

        data_list = retry_url.split('\n')
        n = 0
        error = 0
        save_share_path = 'share/retry_share_url.txt'
        error_data = []
        for i1 in data_list:
            data = i1.split(' | ')
            if data and len(data) == 4:
                first_dir = data[-3]
                second_dir = data[-2]
                fid = data[-1]
                share_error_msg = ''
                for i in range(3):
                    try:
                        task_id = await self.get_share_task_id(fid, second_dir, url_type=url_type,
                                                               expired_type=expired_type,
                                                               password=password)
                        # print('获取到任务ID：', task_id)
                        share_id = await self.get_share_id(task_id)
                        # print('获取到分享ID：', share_id)
                        share_url = await self.submit_share(share_id)
                        with open(save_share_path, 'a', encoding='utf-8') as f:
                            content = f'{n} | {first_dir} | {second_dir} | {share_url}'
                            f.write(content + '\n')
                            custom_print(f'{n}.分享成功 {first_dir}/{second_dir} 文件夹')
                            share_success = True
                            break
                    except Exception as e:
                        # print('分享失败：', e)
                        share_error_msg = e
                        error += 1

                if not share_success:
                    print('分享失败：', share_error_msg)
                    error_data.append(i1)
        error_content = '\n'.join(error_data)
        save_config(path='./share/retry.txt', content=error_content, mode='w')


def load_url_file(fpath: str) -> List[str]:
    with open(fpath, 'r') as f:
        content = f.readlines()

    url_list = [line.strip() for line in content if 'http' in line]
    return url_list


def print_ascii():
    print(r"""║                                     _                                  _                     _       ║
║       __ _   _   _    __ _   _ __  | | __    _ __     __ _   _ __     | |_    ___     ___   | |      ║
║      / _  | | | | |  / _  | | '__| | |/ /   | '_ \   / _  | |  _ \    | __|  / _ \   / _ \  | |      ║
║     | (_| | | |_| | | (_| | | |    |   <    | |_) | | (_| | | | | |   | |_  | (_) | | (_) | | |      ║
║      \__, |  \__,_|  \__,_| |_|    |_|\_\   | .__/   \__,_| |_| |_|    \__|  \___/   \___/  |_|      ║
║         |_|                                 |_|                                                      ║""")


def print_menu() -> None:
    print("╔══════════════════════════════════════════════════════════════════════════════════════════════════════╗")
    print_ascii()
    print("║                                                                                                      ║")
    print("║                                  Author: Hmily  Version: 0.0.3                                       ║")
    print("║                          GitHub: https://github.com/ihmily/QuarkPanTool                              ║")
    print("╠══════════════════════════════════════════════════════════════════════════════════════════════════════╣")
    print(
        r"║  1.分享地址转存文件  2.批量生成分享链接  3.切换网盘保存目录  4.创建网盘文件夹  5.下载到本地  6.登录  7.自动运行  ║")
    print(
        r"║  8.提交分享资源  9.快速调试资源提交  10.数据库迁移  11.重置云搜网提交记录  12.分享浏览统计  13.订阅源统计  ║")
    print(
        r"║  14.修复RSS源记录  15.提交到步游兔  16.提交到Sroad                                                      ║")
    print("╚══════════════════════════════════════════════════════════════════════════════════════════════════════╝")


if __name__ == '__main__':
    quark_file_manager = QuarkPanFileManager(headless=False, slow_mo=500)

    # 创建一个标志，用于控制是否继续运行
    running = True

    while running:
        print_menu()

        to_dir_id, to_dir_name = asyncio.run(quark_file_manager.load_folder_id())

        input_text = input("请输入你的选择(1—16或q退出)：")

        if input_text and input_text.strip() in ['q', 'Q']:
            print("已退出程序！")
            running = False
            sys.exit(0)

        if input_text and input_text.strip() in [str(i) for i in range(1, 17)]:
            if input_text.strip() == '1':
                save_option = input("是否批量转存(1是 2否)：")
                if save_option and save_option == '1':
                    try:
                        urls = load_url_file('./url.txt')
                        if not urls:
                            custom_print('\n分享地址为空！请先在url.txt文件中输入分享地址(一行一个)')
                            continue

                        custom_print(f"\r检测到url.txt文件中有{len(urls)}条分享链接")
                        ok = input("请你确认是否开始批量保存(确认请按2):")
                        if ok and ok.strip() == '2':
                            for index, url in enumerate(urls):
                                print(f"正在转存第{index + 1}个")
                                asyncio.run(quark_file_manager.run(url.strip(), to_dir_id))
                    except FileNotFoundError:
                        with open('url.txt', 'w', encoding='utf-8'):
                            sys.exit(-1)
                else:
                    url = input("请输入夸克文件分享地址：")
                    if url and len(url.strip()) > 20:
                        asyncio.run(quark_file_manager.run(url.strip(), to_dir_id))

            elif input_text.strip() == '2':
                share_option = input("请输入你的选择(1分享 2重试分享)：")
                if share_option and share_option == '1':
                    url = input("请输入需要分享的文件夹网页端页面地址：")
                    if not url or len(url.strip()) < 20:
                        continue
                else:
                    try:
                        url = read_config(path='./share/retry.txt', mode='r')
                        if not url:
                            print('\nretry.txt 为空！请检查文件')
                            continue
                    except FileNotFoundError:
                        save_config('./share/retry.txt', content='')
                        print('\nshare/retry.txt 文件为空！')
                        continue

                expired_option = {"1": 2, "2": 3, "3": 4, "4": 1}
                print("1.1天  2.7天  3.30天  4.永久")
                select_option = input("请输入分享时长选项：")
                _expired_type = expired_option[select_option] if select_option in expired_option else 4
                is_private = input("是否加密(1否/2是)：")
                url_encrypt = 2 if is_private == '2' else 1
                passcode = input('请输入你想设置的分享提取码(直接回车，可随机生成):') if url_encrypt == 2 else ''
                if share_option and share_option == '1':
                    asyncio.run(quark_file_manager.share_run(
                        url.strip(), folder_id=to_dir_id, url_type=int(url_encrypt),
                        expired_type=int(_expired_type), password=passcode))
                else:
                    asyncio.run(quark_file_manager.share_run_retry(url.strip(), url_type=url_encrypt,
                                                                   expired_type=_expired_type, password=passcode))

            elif input_text.strip() == '3':
                to_dir_id, to_dir_name = asyncio.run(quark_file_manager.load_folder_id(renew=True))
                custom_print(f"已切换保存目录至网盘 {to_dir_name} 文件夹\n")

            elif input_text.strip() == '4':
                create_name = input("请输入需要创建的文件夹名称：")
                if create_name:
                    asyncio.run(quark_file_manager.create_dir(create_name.strip()))
                else:
                    custom_print("创建的文件夹名称不可为空！")

            elif input_text.strip() == '5':
                try:
                    is_batch = input("输入你的选择(1单个地址下载，2批量下载):")
                    if is_batch:
                        if is_batch.strip() == '1':
                            url = input("请输入夸克文件分享地址：")
                            asyncio.run(quark_file_manager.run(url.strip(), to_dir_id, download=True))
                        elif is_batch.strip() == '2':
                            urls = load_url_file('./url.txt')
                            if not urls:
                                print('\n分享地址为空！请先在url.txt文件中输入分享地址(一行一个)')
                                continue

                            for index, url in enumerate(urls):
                                asyncio.run(quark_file_manager.run(url.strip(), to_dir_id, download=True))

                except FileNotFoundError:
                    with open('url.txt', 'w', encoding='utf-8'):
                        sys.exit(-1)

            elif input_text.strip() == '6':
                save_config(f'{CONFIG_DIR}/cookies.txt', '')
                quark_file_manager = QuarkPanFileManager(headless=False, slow_mo=500)
                quark_file_manager.get_cookies()

            elif input_text.strip() == '7':
                # 重新加载配置，确保使用最新的配置
                quark_file_manager.rss_config = quark_file_manager.load_rss_config()
                custom_print("已重新加载RSS配置")

                # 确保资源提交管理器已初始化，并包含步游兔和Sroad提交器
                resource_submit_config = quark_file_manager.rss_config.get("resource_submit", {})

                # 确保submitters列表包含buyutu、sroad和macyeah
                if "submitters" in resource_submit_config:
                    if "buyutu" not in resource_submit_config["submitters"]:
                        resource_submit_config["submitters"].append("buyutu")
                        custom_print("已添加步游兔提交器到配置")
                    if "sroad" not in resource_submit_config["submitters"]:
                        resource_submit_config["submitters"].append("sroad")
                        custom_print("已添加Sroad提交器到配置")
                    if "macyeah" not in resource_submit_config["submitters"]:
                        resource_submit_config["submitters"].append("macyeah")
                        custom_print("已添加马克耶网盘提交器到配置")

                # 确保buyutu配置存在
                if "buyutu" not in resource_submit_config:
                    resource_submit_config["buyutu"] = {
                        "enabled": True,
                        "api_url": "https://www.buyutu.com/sub",
                        "batch_size": 10,
                        "use_proxy": False,
                        "retry_count": 3,
                        "delay": {
                            "min": 1.0,
                            "max": 3.0
                        },
                        "random_ua": True
                    }
                    custom_print("已添加步游兔默认配置")

                # 确保sroad配置存在
                if "sroad" not in resource_submit_config:
                    resource_submit_config["sroad"] = {
                        "enabled": True,
                        "api_url": "https://hk10g.sroad.win/sub",
                        "batch_size": 10,
                        "use_proxy": False,
                        "retry_count": 3,
                        "delay": {
                            "min": 1.0,
                            "max": 3.0
                        },
                        "random_ua": True
                    }
                    custom_print("已添加Sroad默认配置")

                # 确保macyeah配置存在
                if "macyeah" not in resource_submit_config:
                    resource_submit_config["macyeah"] = {
                        "enabled": True,
                        "login_url": "https://154.21.90.33/login",
                        "submit_url": "https://154.21.90.33/feedback",
                        "batch_size": 8,
                        "use_proxy": False,
                        "timeout": 60,
                        "retry_times": 3,
                        "retry_interval": 5,
                        "login_credentials": {
                            "email": "<EMAIL>",
                            "password": "albert4417"
                        }
                    }
                    custom_print("已添加马克耶网盘默认配置")

                # 重新初始化资源提交管理器
                quark_file_manager.resource_submit_manager = ResourceSubmitManager(resource_submit_config)
                custom_print("资源提交管理器已更新，包含步游兔、Sroad和马克耶网盘提交器")

                # 确保share_records表中存在提交相关的字段
                import quark_resource_submit
                quark_resource_submit.ensure_submit_fields_exist()

                # 初始化新的提交器并检查提交状态
                quark_resource_submit.initialize_new_submitter("buyutu")
                quark_resource_submit.initialize_new_submitter("sroad")
                quark_resource_submit.initialize_new_submitter("macyeah")

                auto_option = input("请选择自动运行模式(1单次运行 2定时运行)：")
                if auto_option and auto_option.strip() == '1':
                    custom_print("开始单次自动运行...")
                    asyncio.run(quark_file_manager.auto_run())
                elif auto_option and auto_option.strip() == '2':
                    interval = quark_file_manager.rss_config.get("auto_run_interval", 3600)
                    custom_print(f"开始定时自动运行，间隔 {interval} 秒...")
                    custom_print(f"按 Ctrl+C 可以停止定时运行并返回主菜单")

                    try:
                        # 使用正确的定时运行方法
                        asyncio.run(quark_file_manager.scheduled_auto_run())
                    except KeyboardInterrupt:
                        custom_print("用户中断，停止定时运行")

                    custom_print("定时运行已停止，返回主菜单")
                else:
                    custom_print("输入无效，返回主菜单")

            elif input_text.strip() == '8':
                # 重新加载配置，确保使用最新的配置
                quark_file_manager.rss_config = quark_file_manager.load_rss_config()
                custom_print("已重新加载RSS配置")

                # 初始化资源提交管理器
                if not quark_file_manager.resource_submit_manager:
                    resource_submit_config = quark_file_manager.rss_config.get("resource_submit", {})

                    # 确保submitters列表包含buyutu和sroad
                    if "submitters" in resource_submit_config:
                        if "buyutu" not in resource_submit_config["submitters"]:
                            resource_submit_config["submitters"].append("buyutu")
                            custom_print("已添加步游兔提交器到配置")
                        if "sroad" not in resource_submit_config["submitters"]:
                            resource_submit_config["submitters"].append("sroad")
                            custom_print("已添加Sroad提交器到配置")

                    # 确保buyutu配置存在
                    if "buyutu" not in resource_submit_config:
                        resource_submit_config["buyutu"] = {
                            "enabled": True,
                            "api_url": "https://www.buyutu.com/sub",
                            "batch_size": 10,
                            "use_proxy": False,
                            "retry_count": 3,
                            "delay": {
                                "min": 1.0,
                                "max": 3.0
                            },
                            "random_ua": True
                        }
                        custom_print("已添加步游兔默认配置")

                    # 确保sroad配置存在
                    if "sroad" not in resource_submit_config:
                        resource_submit_config["sroad"] = {
                            "enabled": True,
                            "api_url": "https://hk10g.sroad.win/sub",
                            "batch_size": 10,
                            "use_proxy": False,
                            "retry_count": 3,
                            "delay": {
                                "min": 1.0,
                                "max": 3.0
                            },
                            "random_ua": True
                        }
                        custom_print("已添加Sroad默认配置")

                    quark_file_manager.resource_submit_manager = ResourceSubmitManager(resource_submit_config)
                    custom_print("资源提交管理器初始化完成，包含步游兔和Sroad提交器")

                # 确保share_records表中存在提交相关的字段
                quark_resource_submit.ensure_submit_fields_exist()

                # 初始化新的提交器并检查提交状态
                quark_resource_submit.initialize_new_submitter("buyutu")
                quark_resource_submit.initialize_new_submitter("sroad")
                quark_resource_submit.initialize_new_submitter("macyeah")

                custom_print("=" * 50)
                custom_print("提示: 如果您看不到未提交到某个站点的资源，可能是因为:")
                custom_print("1. 数据库中没有任何分享链接记录，请先获取RSS源并生成分享链接")
                custom_print("2. 所有分享链接都已经提交到该站点")
                custom_print("3. 该站点的提交器未正确初始化")
                custom_print("=" * 50)

                submit_option = input("请选择提交方式(1提交指定链接 2查询未提交的记录)：")

                if submit_option and submit_option.strip() == '1':
                    # 提交指定链接
                    links_input = input("请输入要提交的分享链接(多个链接用空格分隔)：")
                    if links_input and links_input.strip():
                        links = [link.strip() for link in links_input.split() if link.strip()]
                        if links:
                            custom_print(f"准备提交 {len(links)} 个分享链接")
                            result = asyncio.run(quark_resource_submit.submit_share_links(
                                quark_file_manager.resource_submit_manager, links))

                            if result.get("code") == 0:
                                custom_print(f"提交分享资源成功: {result.get('message')}")
                            else:
                                custom_print(f"提交分享资源失败: {result.get('message')}", error_msg=True)
                        else:
                            custom_print("没有有效的链接可提交", error_msg=True)
                    else:
                        custom_print("未输入任何链接", error_msg=True)

                elif submit_option and submit_option.strip() == '2':
                    # 查询未提交的记录
                    limit_input = input("请输入要查询的最大记录数(默认50)：")
                    try:
                        limit = int(limit_input) if limit_input.strip() else 50
                    except ValueError:
                        limit = 50
                        custom_print(f"输入无效，使用默认值: {limit}")

                    custom_print(f"准备查询最多 {limit} 条未提交的分享记录")

                    # 显示正在查询的提交器类型
                    if quark_file_manager.resource_submit_manager:
                        submitters = quark_file_manager.resource_submit_manager.get_config().get("submitters", [])
                        custom_print(f"当前启用的提交器: {', '.join(submitters)}")
                        if "sroad" in submitters:
                            custom_print("已启用Sroad提交器，将检查未提交到Sroad的资源")

                    result = asyncio.run(quark_resource_submit.submit_unsubmitted_links(
                        quark_file_manager.resource_submit_manager, limit))

                    if result.get("code") == 0:
                        custom_print(f"提交未提交的分享记录成功: {result.get('message')}")

                        # 显示Sroad站点的提交结果
                        if "details" in result and "sroad" in result["details"]:
                            sroad_result = result["details"]["sroad"]
                            custom_print("=" * 50)
                            custom_print("Sroad站点提交结果:")
                            custom_print(f"状态: {'成功' if sroad_result.get('code') == 0 else '失败'}")
                            custom_print(f"消息: {sroad_result.get('message', '无消息')}")
                            custom_print("=" * 50)
                    else:
                        custom_print(f"提交未提交的分享记录失败: {result.get('message')}", error_msg=True)
                else:
                    custom_print("输入无效，返回主菜单")

            elif input_text.strip() == '9':
                # 快速调试资源提交
                custom_print("=" * 50)
                custom_print("快速调试资源提交功能")
                custom_print("=" * 50)

                # 获取测试链接
                test_url = input("请输入要测试的分享链接(直接回车使用默认测试链接)：")

                if not test_url.strip():
                    # 使用默认测试链接
                    custom_print("使用默认测试链接进行调试")
                    asyncio.run(debug_resource_submit.debug_submit_resource())
                else:
                    # 使用用户输入的测试链接
                    custom_print(f"使用链接 {test_url} 进行调试")
                    asyncio.run(debug_resource_submit.debug_submit_resource(test_url.strip()))

            elif input_text.strip() == '10':
                # 运行数据库迁移
                custom_print("=" * 50)
                custom_print("开始数据库迁移")
                custom_print("=" * 50)

                try:
                    if db_migration.run_migration():
                        custom_print("数据库迁移成功完成")
                    else:
                        custom_print("数据库迁移失败", error_msg=True)
                except Exception as e:
                    custom_print(f"数据库迁移出错: {e}", error_msg=True)

            elif input_text.strip() == '11':
                # 重置云搜网提交记录
                custom_print("=" * 50)
                custom_print("开始重置云搜网提交记录")
                custom_print("=" * 50)

                confirm = input("确定要重置云搜网提交记录吗？这将删除所有云搜网提交记录(y/n): ")
                if confirm.lower() == 'y':
                    try:
                        if reset_yunso_records.run_reset():
                            custom_print("云搜网提交记录重置成功")
                        else:
                            custom_print("云搜网提交记录重置失败", error_msg=True)
                    except Exception as e:
                        custom_print(f"重置云搜网提交记录出错: {e}", error_msg=True)
                else:
                    custom_print("已取消重置操作")

            elif input_text.strip() == '12':
                # 分享浏览统计
                custom_print("=" * 50)
                custom_print("开始获取分享浏览统计数据")
                custom_print("=" * 50)

                # 获取用户输入的记录条数
                record_count_input = input("请输入要查询的分享记录条数(默认100，最大1000): ")
                try:
                    if record_count_input.strip():
                        record_count = int(record_count_input)
                        # 限制最大查询条数为1000
                        if record_count > 1000:
                            custom_print("查询条数过大，已限制为1000条")
                            record_count = 1000
                        elif record_count < 1:
                            custom_print("查询条数必须大于0，已设置为默认值100条")
                            record_count = 100
                    else:
                        record_count = 100
                        custom_print("使用默认查询条数: 100条")
                except ValueError:
                    record_count = 100
                    custom_print("输入无效，使用默认查询条数: 100条")

                try:
                    asyncio.run(quark_file_manager.analyze_share_stats(record_count))
                    custom_print("分享浏览统计数据获取完成")
                except Exception as e:
                    custom_print(f"获取分享浏览统计数据失败: {e}", error_msg=True)

            elif input_text.strip() == '13':
                # 订阅源分享统计
                custom_print("=" * 50)
                custom_print("开始获取订阅源分享统计数据")
                custom_print("=" * 50)

                # 获取用户输入的记录条数
                record_count_input = input("请输入要查询的分享记录条数(默认100，最大1000): ")
                try:
                    if record_count_input.strip():
                        record_count = int(record_count_input)
                        # 限制最大查询条数为1000
                        if record_count > 1000:
                            custom_print("查询条数过大，已限制为1000条")
                            record_count = 1000
                        elif record_count < 1:
                            custom_print("查询条数必须大于0，已设置为默认值100条")
                            record_count = 100
                    else:
                        record_count = 100
                        custom_print("使用默认查询条数: 100条")
                except ValueError:
                    record_count = 100
                    custom_print("输入无效，使用默认查询条数: 100条")

                try:
                    asyncio.run(quark_file_manager.analyze_rss_source_stats(record_count))
                    custom_print("订阅源分享统计数据获取完成")
                except Exception as e:
                    custom_print(f"获取订阅源分享统计数据失败: {e}", error_msg=True)

            elif input_text.strip() == '14':
                try:
                    # 修复RSS源记录
                    fixed_count = quark_file_manager.fix_empty_rss_source_records()
                    if fixed_count > 0:
                        custom_print(f"成功修复 {fixed_count} 条RSS源记录")
                    else:
                        custom_print("没有需要修复的RSS源记录")
                except Exception as e:
                    custom_print(f"修复RSS源记录失败: {e}", error_msg=True)

            elif input_text.strip() == '15':
                # 提交到步游兔
                custom_print("=" * 50)
                custom_print("开始提交到步游兔")
                custom_print("=" * 50)

                # 询问是否重置提交记录
                reset_option = input("是否重置步游兔提交记录？(y/n): ")
                if reset_option.lower() == 'y':
                    try:
                        import submit_to_buyutu
                        if submit_to_buyutu.reset_buyutu_submit_records():
                            custom_print("步游兔提交记录重置成功")
                        else:
                            custom_print("步游兔提交记录重置失败", error_msg=True)
                    except Exception as e:
                        custom_print(f"重置步游兔提交记录出错: {e}", error_msg=True)

                # 提交未提交的记录
                try:
                    import submit_to_buyutu
                    result = asyncio.run(submit_to_buyutu.submit_to_buyutu())
                    if result.get("code") == 0:
                        custom_print(f"提交到步游兔成功: {result.get('message')}")

                        # 显示详细的提交结果
                        details = result.get("details", {})
                        if "buyutu" in details:
                            buyutu_result = details["buyutu"]
                            custom_print("=" * 50)
                            custom_print("步游兔提交详细信息:")
                            custom_print(f"状态: {'成功' if buyutu_result.get('code') == 0 else '失败'}")
                            custom_print(f"消息: {buyutu_result.get('message', '无消息')}")

                            # 显示每批次的提交结果
                            batch_details = buyutu_result.get("details", [])
                            if batch_details:
                                custom_print(f"批次数量: {len(batch_details)}")
                                for i, batch in enumerate(batch_details):
                                    batch_code = batch.get("code", -1)
                                    batch_msg = batch.get("message", "无消息")
                                    batch_links = batch.get("links", [])
                                    batch_response = batch.get("response", {})

                                    custom_print(f"批次 {i+1}:")
                                    custom_print(f"  - 状态: {'成功' if batch_code == 0 else '失败'}")
                                    custom_print(f"  - 消息: {batch_msg}")
                                    custom_print(f"  - 链接数: {len(batch_links)}")

                                    # 显示服务器响应信息
                                    if batch_response:
                                        custom_print("  - 服务器响应详情:")
                                        if isinstance(batch_response, dict):
                                            # 显示响应代码和消息
                                            resp_code = batch_response.get("code", batch_response.get("status", "未知"))
                                            resp_msg = batch_response.get("msg", batch_response.get("message", "无消息"))
                                            custom_print(f"    - 代码: {resp_code}")
                                            custom_print(f"    - 消息: {resp_msg}")

                                            # 显示原始响应内容
                                            raw_response = batch_response.get("raw_response", None)
                                            if raw_response:
                                                if isinstance(raw_response, dict):
                                                    custom_print("    - 原始响应内容:")
                                                    for key, value in raw_response.items():
                                                        custom_print(f"      {key}: {value}")
                                                else:
                                                    custom_print(f"    - 原始响应内容: {raw_response}")
                                        else:
                                            custom_print(f"    - {batch_response}")
                            custom_print("=" * 50)
                    else:
                        custom_print("未获取到步游兔的详细提交信息")

                except Exception as e:
                    custom_print(f"提交到步游兔出错: {e}", error_msg=True)

            elif input_text.strip() == '16':
                # 提交到Sroad
                custom_print("=" * 50)
                custom_print("开始提交到Sroad")
                custom_print("=" * 50)

                # 询问是否重置提交记录
                reset_option = input("是否重置Sroad提交记录？(y/n): ")
                if reset_option.lower() == 'y':
                    try:
                        import submit_to_sroad
                        if submit_to_sroad.reset_sroad_submit_records():
                            custom_print("Sroad提交记录重置成功")
                        else:
                            custom_print("Sroad提交记录重置失败", error_msg=True)
                    except Exception as e:
                        custom_print(f"重置Sroad提交记录出错: {e}", error_msg=True)

                # 提交未提交的记录
                try:
                    import submit_to_sroad
                    result = asyncio.run(submit_to_sroad.submit_to_sroad())
                    if result.get("code") == 0:
                        custom_print(f"提交到Sroad成功: {result.get('message')}")

                        # 显示详细的提交结果
                        details = result.get("details", {})
                        if "sroad" in details:
                            sroad_result = details["sroad"]
                            custom_print("=" * 50)
                            custom_print("Sroad提交详细信息:")
                            custom_print(f"状态: {'成功' if sroad_result.get('code') == 0 else '失败'}")
                            custom_print(f"消息: {sroad_result.get('message', '无消息')}")

                            # 显示每批次的提交结果
                            batch_details = sroad_result.get("details", [])
                            if batch_details:
                                custom_print(f"批次数量: {len(batch_details)}")
                                for i, batch in enumerate(batch_details):
                                    batch_code = batch.get("code", -1)
                                    batch_msg = batch.get("message", "无消息")
                                    batch_links = batch.get("links", [])
                                    batch_response = batch.get("response", {})

                                    custom_print(f"批次 {i+1}:")
                                    custom_print(f"  - 状态: {'成功' if batch_code == 0 else '失败'}")
                                    custom_print(f"  - 消息: {batch_msg}")
                                    custom_print(f"  - 链接数: {len(batch_links)}")

                                    # 显示服务器响应信息
                                    if batch_response:
                                        custom_print("  - 服务器响应详情:")
                                        if isinstance(batch_response, dict):
                                            # 显示响应代码和消息
                                            resp_code = batch_response.get("code", batch_response.get("status", "未知"))
                                            resp_msg = batch_response.get("msg", batch_response.get("message", "无消息"))
                                            custom_print(f"    - 代码: {resp_code}")
                                            custom_print(f"    - 消息: {resp_msg}")

                                            # 显示原始响应内容
                                            raw_response = batch_response.get("raw_response", None)
                                            if raw_response:
                                                if isinstance(raw_response, dict):
                                                    custom_print("    - 原始响应内容:")
                                                    for key, value in raw_response.items():
                                                        custom_print(f"      {key}: {value}")
                                                else:
                                                    custom_print(f"    - 原始响应内容: {raw_response}")

                                            # 显示原始响应文本
                                            raw_text = batch_response.get("raw_text", None)
                                            if raw_text:
                                                custom_print(f"    - 原始响应文本: {raw_text}")
                                        else:
                                            custom_print(f"    - {batch_response}")
                            custom_print("=" * 50)
                    else:
                        custom_print("未获取到Sroad的详细提交信息")

                except Exception as e:
                    custom_print(f"提交到Sroad出错: {e}", error_msg=True)

        else:
            custom_print("输入无效，请重新输入")
