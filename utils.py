import json
import os
import random
import shutil
import string
import time
from datetime import datetime
from typing import Union, Dict, List, Any
from colorama import Fore, Style
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils import get_column_letter


def get_datetime(timestamp: Union[int, float, None] = None, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    if timestamp is None or not isinstance(timestamp, (int, float)):
        return datetime.today().strftime(fmt)
    else:
        dt = datetime.fromtimestamp(timestamp)
        formatted_time = dt.strftime(fmt)
        return formatted_time


def custom_print(message, error_msg=False) -> None:
    if error_msg:
        print(Fore.RED + f'[{get_datetime()}] {message}' + Style.RESET_ALL)
    else:
        print(f'[{get_datetime()}] {message}')


def get_timestamp(length: int) -> int:
    if length == 13:
        return int(time.time()) * 1000
    else:
        return int(time.time())


def save_config(path: str, content: str, mode: str = 'w'):
    with open(path, mode, encoding='utf-8') as f:
        f.write(content)


def read_config(path: str, read_type: str = None, mode: str = 'r') -> Union[dict, str]:
    with open(path, mode, encoding='utf-8') as config_file:
        if read_type != 'json':
            return config_file.read()
        else:
            return json.load(config_file)


def safe_copy(src, dst):
    if not os.path.exists(src):
        print(f"源文件不存在，跳过复制：{src}")
        return

    if os.path.exists(dst):
        os.remove(dst)
        print(f"目标文件已存在，已删除：{dst}")

    try:
        shutil.copy(src, dst)
        print(f"文件已复制到：{dst}")
    except Exception as e:
        print('备份share_url.txt文件错误，', e)


def export_to_excel(data: Dict, filename: str) -> str:
    """
    将数据导出到Excel表格

    Args:
        data: 包含要导出数据的字典
        filename: 导出的文件名

    Returns:
        str: 导出文件的路径
    """
    try:
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "统计数据"

        # 设置样式
        header_fill = PatternFill(start_color="1F4E78", end_color="1F4E78", fill_type="solid")
        header_font = Font(color="FFFFFF", bold=True, size=12)
        header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)

        border = Border(
            left=Side(border_style="thin", color="000000"),
            right=Side(border_style="thin", color="000000"),
            top=Side(border_style="thin", color="000000"),
            bottom=Side(border_style="thin", color="000000")
        )

        # 添加总览信息
        if "overview" in data:
            row = 1
            ws.cell(row=row, column=1, value="总体统计").font = Font(bold=True, size=14)
            ws.merge_cells(start_row=row, start_column=1, end_row=row, end_column=6)
            ws.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            for key, value in data["overview"].items():
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=value)
                row += 1

            row += 1

        # 添加排行信息
        if "rankings" in data:
            row = row if "overview" in data else 1
            ws.cell(row=row, column=1, value="最受欢迎的分享").font = Font(bold=True, size=14)
            ws.merge_cells(start_row=row, start_column=1, end_row=row, end_column=6)
            ws.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            for key, value in data["rankings"].items():
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=value)
                row += 1

            row += 1

        # 添加转化率信息
        if "conversion" in data:
            row = row if "overview" in data or "rankings" in data else 1
            ws.cell(row=row, column=1, value="转化率统计").font = Font(bold=True, size=14)
            ws.merge_cells(start_row=row, start_column=1, end_row=row, end_column=6)
            ws.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            for key, value in data["conversion"].items():
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=value)
                row += 1

            row += 1

        # 添加趋势信息
        if "trends" in data and data["trends"]:
            row = row if "overview" in data or "rankings" in data or "conversion" in data else 1
            ws.cell(row=row, column=1, value="按日期分析浏览趋势").font = Font(bold=True, size=14)
            ws.merge_cells(start_row=row, start_column=1, end_row=row, end_column=6)
            ws.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = ["日期", "浏览量", "保存次数", "下载次数"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for trend in data["trends"]:
                ws.cell(row=row, column=1, value=trend["date"])
                ws.cell(row=row, column=2, value=trend["views"])
                ws.cell(row=row, column=3, value=trend["saves"])
                ws.cell(row=row, column=4, value=trend["downloads"])

                # 应用边框
                for col in range(1, 5):
                    ws.cell(row=row, column=col).border = border

                row += 1

            row += 1

        # 添加详情表格
        if "details" in data and data["details"]:
            row = row if any(key in data for key in ["overview", "rankings", "conversion", "trends"]) else 1
            ws.cell(row=row, column=1, value="分享详情").font = Font(bold=True, size=14)
            ws.merge_cells(start_row=row, start_column=1, end_row=row, end_column=10)
            ws.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = data["details"][0].keys()
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for detail in data["details"]:
                for col, (key, value) in enumerate(detail.items(), 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.border = border
                    if isinstance(value, (int, float)) and key not in ["大小(MB)"]:
                        cell.alignment = Alignment(horizontal="right")
                row += 1

        # 添加订阅源统计
        if "source_stats" in data and data["source_stats"]:
            # 创建新的工作表
            ws_sources = wb.create_sheet(title="订阅源统计")

            row = 1
            ws_sources.cell(row=row, column=1, value="订阅源分享统计").font = Font(bold=True, size=14)
            ws_sources.merge_cells(start_row=row, start_column=1, end_row=row, end_column=7)
            ws_sources.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = data["source_stats"][0].keys()
            for col, header in enumerate(headers, 1):
                cell = ws_sources.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for source in data["source_stats"]:
                for col, (key, value) in enumerate(source.items(), 1):
                    cell = ws_sources.cell(row=row, column=col, value=value)
                    cell.border = border
                    if isinstance(value, (int, float)):
                        cell.alignment = Alignment(horizontal="right")
                row += 1

        # 添加订阅源详情
        if "source_details" in data and data["source_details"]:
            # 使用已有的订阅源统计工作表或创建新的
            if "source_stats" in data and data["source_stats"]:
                ws_details = wb["订阅源统计"]
                row = ws_details.max_row + 2
            else:
                ws_details = wb.create_sheet(title="订阅源详情")
                row = 1

            ws_details.cell(row=row, column=1, value="最受欢迎的订阅源详情").font = Font(bold=True, size=14)
            ws_details.merge_cells(start_row=row, start_column=1, end_row=row, end_column=8)
            ws_details.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = data["source_details"][0].keys()
            for col, header in enumerate(headers, 1):
                cell = ws_details.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for detail in data["source_details"]:
                for col, (key, value) in enumerate(detail.items(), 1):
                    cell = ws_details.cell(row=row, column=col, value=value)
                    cell.border = border
                    if isinstance(value, (int, float)) and key not in ["大小(MB)"]:
                        cell.alignment = Alignment(horizontal="right")
                row += 1

        # 添加未转存链接的订阅源统计数据
        if "unsaved_links" in data and data["unsaved_links"]:
            # 创建新的工作表
            ws_unsaved = wb.create_sheet(title="未转存链接统计")

            row = 1
            ws_unsaved.cell(row=row, column=1, value="未转存链接的订阅源统计").font = Font(bold=True, size=14)
            ws_unsaved.merge_cells(start_row=row, start_column=1, end_row=row, end_column=6)
            ws_unsaved.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = data["unsaved_links"][0].keys()
            for col, header in enumerate(headers, 1):
                cell = ws_unsaved.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for source in data["unsaved_links"]:
                for col, (key, value) in enumerate(source.items(), 1):
                    cell = ws_unsaved.cell(row=row, column=col, value=value)
                    cell.border = border
                    if isinstance(value, (int, float)):
                        cell.alignment = Alignment(horizontal="right")
                row += 1

        # 添加从未获取到分享链接的订阅源统计数据
        if "never_fetched_sources" in data and data["never_fetched_sources"]:
            # 创建新的工作表
            ws_never_fetched = wb.create_sheet(title="未获取链接的订阅源")

            row = 1
            ws_never_fetched.cell(row=row, column=1, value="从未获取到分享链接的订阅源").font = Font(bold=True, size=14)
            ws_never_fetched.merge_cells(start_row=row, start_column=1, end_row=row, end_column=2)
            ws_never_fetched.cell(row=row, column=1).alignment = Alignment(horizontal="center")

            row += 2
            # 添加表头
            headers = data["never_fetched_sources"][0].keys()
            for col, header in enumerate(headers, 1):
                cell = ws_never_fetched.cell(row=row, column=col, value=header)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            row += 1
            # 添加数据
            for source in data["never_fetched_sources"]:
                for col, (key, value) in enumerate(source.items(), 1):
                    cell = ws_never_fetched.cell(row=row, column=col, value=value)
                    cell.border = border
                row += 1

        # 调整列宽
        for ws in wb.worksheets:
            for col in range(1, ws.max_column + 1):
                max_length = 0
                column = get_column_letter(col)
                for row in range(1, ws.max_row + 1):
                    cell = ws[f"{column}{row}"]
                    if cell.value:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                adjusted_width = (max_length + 2) * 1.2
                ws.column_dimensions[column].width = adjusted_width

        # 保存文件
        os.makedirs("exports", exist_ok=True)
        file_path = f"exports/{filename}"
        wb.save(file_path)
        custom_print(f"数据已导出到 {file_path}")
        return file_path
    except Exception as e:
        custom_print(f"导出Excel失败: {e}", error_msg=True)
        return None


def generate_random_code(length=4):
    characters = string.ascii_letters + string.digits
    random_code = ''.join(random.choice(characters) for _ in range(length))
    return random_code
