#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def get_datetime(fmt="%Y-%m-%d %H:%M:%S"):
    """获取当前时间的格式化字符串"""
    return datetime.now().strftime(fmt)

def print_with_time(message, error=False):
    """打印带时间的消息"""
    prefix = "[ERROR] " if error else ""
    print(f"[{get_datetime()}] {prefix}{message}")

def ensure_share_records_table():
    """确保share_records表存在"""
    os.makedirs('data', exist_ok=True)
    conn = sqlite3.connect('data/quark_links.db')
    cursor = conn.cursor()
    
    # 检查分享记录表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
    share_table_exists = cursor.fetchone()
    
    if not share_table_exists:
        # 创建分享记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS share_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            folder_id TEXT NOT NULL,
            folder_name TEXT NOT NULL,
            subfolder_id TEXT NOT NULL,
            subfolder_name TEXT NOT NULL,
            share_url TEXT NOT NULL,
            share_time TEXT NOT NULL,
            status INTEGER DEFAULT 1,
            error_message TEXT
        )
        ''')
        print_with_time("手动创建分享记录表成功")
    
    conn.commit()
    conn.close()

def is_folder_shared(subfolder_id):
    """
    检查文件夹是否已经分享过
    
    Args:
        subfolder_id: 子文件夹ID
        
    Returns:
        bool: 如果文件夹已经分享过且分享成功，返回True，否则返回False
    """
    # 确保表存在
    ensure_share_records_table()
    
    conn = sqlite3.connect('data/quark_links.db')
    cursor = conn.cursor()
    
    # 查询是否有成功的分享记录
    cursor.execute(
        "SELECT id, subfolder_name, share_url FROM share_records WHERE subfolder_id = ? AND status = 1 AND share_url != ''", 
        (subfolder_id,)
    )
    result = cursor.fetchone()
    conn.close()
    
    if result:
        print_with_time(f"找到分享记录: ID={result[0]}, 文件夹名={result[1]}, 分享链接={result[2]}")
        return True
    else:
        print_with_time(f"未找到文件夹 {subfolder_id} 的分享记录")
        return False

def save_share_record(folder_id, folder_name, subfolder_id, subfolder_name, share_url, status=1, error_message=""):
    """
    保存分享记录到数据库
    
    Args:
        folder_id: 父文件夹ID
        folder_name: 父文件夹名称
        subfolder_id: 子文件夹ID
        subfolder_name: 子文件夹名称
        share_url: 分享链接
        status: 状态，1=成功，0=失败
        error_message: 如果分享失败，记录错误信息
    """
    # 确保表存在
    ensure_share_records_table()
    
    current_time = get_datetime()
    
    conn = sqlite3.connect('data/quark_links.db')
    cursor = conn.cursor()
    
    # 检查是否已存在相同的记录
    cursor.execute(
        "SELECT id FROM share_records WHERE subfolder_id = ? AND share_url = ?",
        (subfolder_id, share_url)
    )
    existing_record = cursor.fetchone()
    
    if existing_record:
        # 更新现有记录
        cursor.execute(
            """UPDATE share_records SET
               folder_id = ?, folder_name = ?, subfolder_name = ?,
               share_time = ?, status = ?, error_message = ?
               WHERE subfolder_id = ? AND share_url = ?""",
            (folder_id, folder_name, subfolder_name, current_time,
             status, error_message, subfolder_id, share_url)
        )
        print_with_time(f"更新分享记录: {subfolder_name}")
    else:
        # 插入新记录
        cursor.execute(
            """INSERT INTO share_records
               (folder_id, folder_name, subfolder_id, subfolder_name,
                share_url, share_time, status, error_message)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
            (folder_id, folder_name, subfolder_id, subfolder_name,
             share_url, current_time, status, error_message)
        )
        print_with_time(f"保存分享记录: {subfolder_name}")
    
    conn.commit()
    conn.close()

def main():
    """主函数，测试重复分享检查功能"""
    print_with_time("开始测试重复分享检查功能")
    
    # 测试数据
    test_folders = [
        {
            "folder_id": "folder123",
            "folder_name": "2025-05-20",
            "subfolder_id": "subfolder456",
            "subfolder_name": "测试文件夹",
            "share_url": "https://pan.quark.cn/s/test123456"
        },
        {
            "folder_id": "folder123",
            "folder_name": "2025-05-20",
            "subfolder_id": "subfolder789",
            "subfolder_name": "新测试文件夹",
            "share_url": "https://pan.quark.cn/s/test789012"
        }
    ]
    
    # 测试每个文件夹
    for folder in test_folders:
        subfolder_id = folder["subfolder_id"]
        subfolder_name = folder["subfolder_name"]
        
        print_with_time(f"检查文件夹: {subfolder_name} (ID: {subfolder_id})")
        
        # 检查文件夹是否已经分享过
        if is_folder_shared(subfolder_id):
            print_with_time(f"文件夹 {subfolder_name} 已经分享过，跳过分享")
        else:
            print_with_time(f"文件夹 {subfolder_name} 未分享过，进行分享")
            
            # 模拟分享操作
            print_with_time(f"模拟分享文件夹: {subfolder_name}")
            
            # 保存分享记录
            save_share_record(
                folder_id=folder["folder_id"],
                folder_name=folder["folder_name"],
                subfolder_id=subfolder_id,
                subfolder_name=subfolder_name,
                share_url=folder["share_url"]
            )
    
    print_with_time("测试完成")

if __name__ == "__main__":
    main()
