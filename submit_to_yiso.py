#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sqlite3
from resource_submitters.resource_submit_manager import ResourceSubmitManager
from utils import custom_print

async def submit_to_yiso():
    """提交所有未提交到易搜站点的记录"""
    # 初始化资源提交管理器
    config = {
        "enabled": True,
        "auto_submit": True,
        "submitters": ["yiso"],
        "submit_delay": {
            "min": 1,
            "max": 2
        },
        "avoid_duplicate": True,
        "yiso": {
            "enabled": True,
            "api_url": "https://yiso.fun/api/member/share",
            "batch_size": 1,
            "use_proxy": False
        }
    }
    
    resource_submit_manager = ResourceSubmitManager(config)
    
    try:
        # 从数据库中获取所有成功分享的记录
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()
        
        # 获取所有成功分享的记录
        cursor.execute("SELECT share_url FROM share_records WHERE status = 1 AND share_url != ''")
        all_share_urls = [row[0] for row in cursor.fetchall()]
        
        # 获取已提交到易搜的记录
        cursor.execute("SELECT share_url FROM submit_records WHERE submitter_type = 'yiso' AND status = 1")
        submitted_urls = [row[0] for row in cursor.fetchall()]
        
        # 过滤出未提交到易搜的记录
        unsubmitted_urls = [url for url in all_share_urls if url not in submitted_urls]
        
        custom_print(f"总共有 {len(all_share_urls)} 条分享记录")
        custom_print(f"已提交到易搜的记录: {len(submitted_urls)} 条")
        custom_print(f"未提交到易搜的记录: {len(unsubmitted_urls)} 条")
        
        if not unsubmitted_urls:
            custom_print("没有未提交到易搜的记录")
            return
        
        # 提交未提交的记录
        custom_print(f"开始提交 {len(unsubmitted_urls)} 条记录到易搜...")
        
        # 分批提交，每批10条
        batch_size = 10
        total_batches = (len(unsubmitted_urls) + batch_size - 1) // batch_size
        
        success_count = 0
        error_count = 0
        
        for i in range(0, len(unsubmitted_urls), batch_size):
            batch = unsubmitted_urls[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            custom_print(f"正在提交第 {batch_num}/{total_batches} 批 ({len(batch)} 条记录)...")
            
            # 提交链接
            result = await resource_submit_manager.submit_links(batch)
            
            if result.get("code") == 0:
                custom_print(f"第 {batch_num} 批提交成功: {result.get('message')}")
                
                # 统计成功数量
                if "details" in result and "yiso" in result["details"]:
                    yiso_result = result["details"]["yiso"]
                    if "details" in yiso_result:
                        for batch_result in yiso_result["details"]:
                            if batch_result.get("code") == 0:
                                success_count += len(batch_result.get("links", []))
                            else:
                                error_count += len(batch_result.get("links", []))
            else:
                custom_print(f"第 {batch_num} 批提交失败: {result.get('message')}", error_msg=True)
                error_count += len(batch)
            
            # 每批之间等待5-10秒
            if i + batch_size < len(unsubmitted_urls):
                delay = 5
                custom_print(f"等待 {delay} 秒后提交下一批...")
                await asyncio.sleep(delay)
        
        custom_print("=" * 50)
        custom_print("提交统计:")
        custom_print(f"- 总记录数: {len(unsubmitted_urls)}")
        custom_print(f"- 成功提交: {success_count}")
        custom_print(f"- 提交失败: {error_count}")
        custom_print("=" * 50)
        
    finally:
        # 关闭资源提交管理器
        await resource_submit_manager.close()

if __name__ == "__main__":
    asyncio.run(submit_to_yiso())
