{"schemaVersion": 37, "addons": [{"id": "<EMAIL>", "syncGUID": "{e396fc28-3d49-49bb-bd98-40cf63fdc6ae}", "version": "1.0.1", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Form Autofill", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1742127288184, "updateDate": 1742127288184, "applyBackgroundUpdates": 1, "path": "/Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{dc089546-1d64-422f-bc17-767ab09531ba}", "version": "1.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Picture-In-Picture", "description": "Fixes for web compatibility with Picture-in-Picture", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1742127288184, "updateDate": 1742127288184, "applyBackgroundUpdates": 1, "path": "/Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "88.0a1", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{d7fb512c-776a-4452-be6b-247fb73a77c7}", "version": "39.0.1", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Firefox Screenshots", "description": "Take clips and screenshots from the Web and save them temporarily or permanently.", "creator": "Mozilla <<EMAIL>>", "homepageURL": "https://github.com/mozilla-services/screenshots", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1742127288178, "updateDate": 1742127288178, "applyBackgroundUpdates": 1, "path": "/Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "57.0a1", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["activeTab", "downloads", "tabs", "storage", "notifications", "clipboardWrite", "contextMenus", "mozillaAddons", "telemetry"], "origins": ["<all_urls>", "https://screenshots.firefox.com/", "resource://pdf.js/", "about:reader*", "https://screenshots.firefox.com/*"]}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{fd254f44-98cd-425d-9b49-317988e600ce}", "version": "2.1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "WebCompat Reporter", "description": "Report site compatibility issues on webcompat.com", "creator": "<PERSON> <<EMAIL>>", "homepageURL": "https://github.com/mozilla/webcompat-reporter", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1742127288184, "updateDate": 1742127288184, "applyBackgroundUpdates": 1, "path": "/Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["tabs"], "origins": ["<all_urls>"]}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {"16": "icons/lightbulb.svg", "32": "icons/lightbulb.svg", "48": "icons/lightbulb.svg", "96": "icons/lightbulb.svg", "128": "icons/lightbulb.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{20c1f9d4-e482-4a69-9c81-cbaa952232d1}", "version": "134.5.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Web Compatibility Interventions", "description": "Urgent post-release fixes for web compatibility.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1742127288183, "updateDate": 1742127288183, "applyBackgroundUpdates": 1, "path": "/Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "102.0", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["mozillaAddons", "scripting", "tabs", "webNavigation", "webRequest", "webRequestBlocking"], "origins": ["<all_urls>"]}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///Users/<USER>/Library/Caches/ms-playwright/firefox-1471/firefox/Nightly.app/Contents/Resources/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{750d5e77-a364-43b3-b5c0-f467480d9897}", "version": "1.4.1", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "System theme — auto", "description": "Follow the operating system setting for buttons, menus, and windows.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1747208056478, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://default-theme/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{99ea26c7-9c68-44f1-91d8-4705be627b6e}", "version": "2.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Add-ons Search Detection", "description": "", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1747208057162, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["telemetry", "webRequest", "webRequestBlocking"], "origins": ["<all_urls>"]}, "optionalPermissions": {"permissions": [], "origins": []}, "requestedPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/search-detection/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{505c7022-b04e-4c44-aab2-704aec6d89f3}", "version": "1.3", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Light", "description": "A theme with a light color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1747208057269, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/light/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{20f2a3dc-0565-404b-8a20-ff0785d0aa10}", "version": "1.3.2", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Dark", "description": "A theme with a dark color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1747208057269, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/dark/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{*************-4817-b37d-99e103a955c2}", "version": "1.5", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Firefox Alpenglow", "description": "Use a colorful appearance for buttons, menus, and windows.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1747208057269, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/alpenglow/", "location": "app-builtin"}]}