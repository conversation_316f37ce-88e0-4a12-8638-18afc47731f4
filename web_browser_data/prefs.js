// Mozilla User Preferences

// DO NOT EDIT THIS FILE.
//
// If you make changes to this file while the application is running,
// the changes will be overwritten when the application exits.
//
// To change a preference value, you can either:
// - modify it via the UI (e.g. via about:config in the browser); or
// - set it within a user.js file in your profile.

user_pref("app.normandy.api_url", "");
user_pref("app.normandy.enabled", false);
user_pref("app.normandy.first_run", false);
user_pref("app.normandy.migrationsApplied", 12);
user_pref("app.shield.optoutstudies.enabled", false);
user_pref("app.update.auto", false);
user_pref("app.update.checkInstallTime", false);
user_pref("app.update.disabledForTesting", true);
user_pref("app.update.enabled", false);
user_pref("app.update.lastUpdateTime.browser-cleanup-thumbnails", 0);
user_pref("app.update.lastUpdateTime.region-update-timer", 0);
user_pref("app.update.lastUpdateTime.xpi-signature-verification", 0);
user_pref("app.update.mode", 0);
user_pref("app.update.service.enabled", false);
user_pref("apz.content_response_timeout", 60000);
user_pref("browser.bookmarks.addedImportButton", true);
user_pref("browser.bookmarks.restore_default_bookmarks", false);
user_pref("browser.contentblocking.category", "custom");
user_pref("browser.contextual-services.contextId", "{778e73ba-aa30-496a-92e8-704ab68b4cec}");
user_pref("browser.download.panel.shown", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.avif", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.webp", true);
user_pref("browser.library.activity-stream.enabled", false);
user_pref("browser.migration.version", 150);
user_pref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
user_pref("browser.newtabpage.activity-stream.feeds.topsites", false);
user_pref("browser.newtabpage.activity-stream.impressionId", "{8ecde181-73a8-4d87-9c92-c11dd3f65bae}");
user_pref("browser.newtabpage.activity-stream.showSponsoredTopSites", false);
user_pref("browser.newtabpage.enabled", false);
user_pref("browser.newtabpage.storageVersion", 1);
user_pref("browser.pageActions.persistedActions", "{\"ids\":[\"bookmark\"],\"idsInUrlbar\":[\"bookmark\"],\"idsInUrlbarPreProton\":[],\"version\":1}");
user_pref("browser.pagethumbnails.capturing_disabled", true);
user_pref("browser.pagethumbnails.storage_version", 3);
user_pref("browser.proton.toolbar.version", 3);
user_pref("browser.region.network.url", "");
user_pref("browser.safebrowsing.blockedURIs.enabled", false);
user_pref("browser.safebrowsing.downloads.enabled", false);
user_pref("browser.safebrowsing.malware.enabled", false);
user_pref("browser.safebrowsing.passwords.enabled", false);
user_pref("browser.safebrowsing.phishing.enabled", false);
user_pref("browser.safebrowsing.provider.mozilla.updateURL", "");
user_pref("browser.search.geoSpecificDefaults", false);
user_pref("browser.search.geoSpecificDefaults.url", "");
user_pref("browser.search.update", false);
user_pref("browser.sessionstore.resume_from_crash", false);
user_pref("browser.sessionstore.upgradeBackup.latestBuildID", "20250112125551");
user_pref("browser.shell.checkDefaultBrowser", false);
user_pref("browser.startup.couldRestoreSession.count", 2);
user_pref("browser.startup.homepage", "about:blank");
user_pref("browser.startup.homepage_override.mstone", "ignore");
user_pref("browser.startup.lastColdStartupCheck", **********);
user_pref("browser.startup.page", 0);
user_pref("browser.tabs.disableBackgroundZombification", false);
user_pref("browser.tabs.warnOnCloseOtherTabs", false);
user_pref("browser.tabs.warnOnOpen", false);
user_pref("browser.topsites.contile.enabled", false);
user_pref("browser.translations.enable", false);
user_pref("browser.uiCustomization.state", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"unified-extensions-area\":[],\"nav-bar\":[\"back-button\",\"forward-button\",\"stop-reload-button\",\"customizableui-special-spring1\",\"urlbar-container\",\"customizableui-special-spring2\",\"save-to-pocket-button\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"unified-extensions-button\"],\"TabsToolbar\":[\"firefox-view-button\",\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"vertical-tabs\":[],\"PersonalToolbar\":[\"import-button\",\"personal-bookmarks\"]},\"seen\":[\"developer-button\"],\"dirtyAreaCache\":[\"nav-bar\",\"vertical-tabs\",\"PersonalToolbar\",\"TabsToolbar\"],\"currentVersion\":20,\"newElementCount\":2}");
user_pref("browser.uitour.enabled", false);
user_pref("browser.urlbar.quicksuggest.migrationVersion", 2);
user_pref("browser.urlbar.quicksuggest.scenario", "history");
user_pref("browser.urlbar.suggest.searches", false);
user_pref("browser.usedOnWindows10.introURL", "");
user_pref("browser.warnOnQuit", false);
user_pref("captivedetect.canonicalURL", "");
user_pref("datareporting.dau.cachedUsageProfileID", "beefbeef-beef-beef-beef-beeefbeefbee");
user_pref("datareporting.healthreport.about.reportUrl", "");
user_pref("datareporting.healthreport.documentServerURI", "");
user_pref("datareporting.healthreport.logging.consoleEnabled", false);
user_pref("datareporting.healthreport.service.enabled", false);
user_pref("datareporting.healthreport.service.firstRun", false);
user_pref("datareporting.healthreport.uploadEnabled", false);
user_pref("datareporting.policy.dataSubmissionEnabled", false);
user_pref("datareporting.policy.dataSubmissionPolicyBypassNotification", true);
user_pref("devtools.jsonview.enabled", false);
user_pref("devtools.toolbox.host", "window");
user_pref("distribution.iniFile.exists.appversion", "134.0");
user_pref("distribution.iniFile.exists.value", false);
user_pref("dom.disable_open_during_load", false);
user_pref("dom.fetchKeepalive.enabled", false);
user_pref("dom.file.createInChild", true);
user_pref("dom.filesystem.pathcheck.disabled", true);
user_pref("dom.iframe_lazy_loading.enabled", false);
user_pref("dom.input_events.security.minNumTicks", 0);
user_pref("dom.input_events.security.minTimeElapsedInMS", 0);
user_pref("dom.ipc.processCount", 60000);
user_pref("dom.ipc.processPrelaunch.enabled", false);
user_pref("dom.ipc.reportProcessHangs", false);
user_pref("dom.max_script_run_time", 0);
user_pref("dom.push.connection.enabled", false);
user_pref("dom.push.serverURL", "");
user_pref("extensions.activeThemeID", "<EMAIL>");
user_pref("extensions.autoDisableScopes", 0);
user_pref("extensions.blocklist.enabled", false);
user_pref("extensions.blocklist.pingCountVersion", 0);
user_pref("extensions.databaseSchema", 37);
user_pref("extensions.enabledScopes", 5);
user_pref("extensions.formautofill.addresses.supported", "off");
user_pref("extensions.formautofill.creditCards.reauth.optout", "MDIEEPgAAAAAAAAAAAAAAAAAAAEwFAYIKoZIhvcNAwcECEhkaRzAu7b2BAif8faJPYXLiw==");
user_pref("extensions.formautofill.creditCards.supported", "off");
user_pref("extensions.getAddons.cache.enabled", false);
user_pref("extensions.installDistroAddons", false);
user_pref("extensions.lastAppBuildId", "20250112125551");
user_pref("extensions.lastAppVersion", "134.0");
user_pref("extensions.lastPlatformVersion", "134.0");
user_pref("extensions.pendingOperations", false);
user_pref("extensions.pictureinpicture.enable_picture_in_picture_overrides", true);
user_pref("extensions.pocket.enabled", false);
user_pref("extensions.screenshots.disabled", true);
user_pref("extensions.screenshots.upload-disabled", true);
user_pref("extensions.systemAddonSet", "{\"schema\":1,\"addons\":{}}");
user_pref("extensions.update.enabled", false);
user_pref("extensions.update.notifyUser", false);
user_pref("extensions.webcompat.enable_shims", true);
user_pref("extensions.webcompat.perform_injections", true);
user_pref("extensions.webcompat.perform_ua_overrides", true);
user_pref("<EMAIL>", true);
user_pref("extensions.webextensions.uuids", "{\"<EMAIL>\":\"1224e1d2-ea72-4e0e-926f-21e492e89f2b\",\"<EMAIL>\":\"70837bdd-8df6-4ac3-9a26-85e906f0acb9\",\"<EMAIL>\":\"0b4531bd-2f71-457c-867d-d694e5bbda25\",\"<EMAIL>\":\"3a3faf02-ab92-47a6-a948-2e009b1d3c5f\",\"<EMAIL>\":\"fbd3e055-17c7-4fb4-b088-f6bda07ff709\",\"<EMAIL>\":\"f4ac3fea-8d39-43de-8c04-f5797b12a30a\",\"<EMAIL>\":\"26c7ee40-ed10-46df-ba26-a2a1bc81e10f\"}");
user_pref("extensions.webservice.discoverURL", "");
user_pref("fission.bfcacheInParent", false);
user_pref("fission.webContentIsolationStrategy", 0);
user_pref("focusmanager.testmode", true);
user_pref("gecko.handlerService.defaultHandlersVersion", 1);
user_pref("general.useragent.updates.enabled", false);
user_pref("geo.provider.testing", true);
user_pref("geo.wifi.scan", false);
user_pref("gfx.color_management.mode", 0);
user_pref("gfx.color_management.rendering_intent", 3);
user_pref("hangmonitor.timeout", 0);
user_pref("javascript.options.showInConsole", true);
user_pref("layout.spellcheckDefault", 0);
user_pref("media.getdisplaymedia.screencapturekit.enabled", false);
user_pref("media.getdisplaymedia.screencapturekit.picker.enabled", false);
user_pref("media.gmp-manager.updateEnabled", false);
user_pref("media.gmp.storage.version.observed", 1);
user_pref("network.captive-portal-service.enabled", false);
user_pref("network.connectivity-service.enabled", false);
user_pref("network.cookie.cookieBehavior", 4);
user_pref("network.http.phishy-userpass-length", 255);
user_pref("network.http.speculative-parallel-limit", 0);
user_pref("network.manage-offline-status", false);
user_pref("network.sntp.pools", "");
user_pref("pdfjs.disabled", true);
user_pref("pdfjs.enabledCache.state", false);
user_pref("permissions.isolateBy.userContext", true);
user_pref("plugin.state.flash", 0);
user_pref("privacy.bounceTrackingProtection.hasMigratedUserActivationData", true);
user_pref("privacy.sanitize.clearOnShutdown.hasMigratedToNewPrefs2", true);
user_pref("privacy.sanitize.pending", "[]");
user_pref("prompts.contentPromptSubDialog", false);
user_pref("security.certerrors.mitm.priming.enabled", false);
user_pref("security.fileuri.strict_origin_policy", false);
user_pref("security.notification_enable_delay", 0);
user_pref("security.sandbox.warn_unprivileged_namespaces", false);
user_pref("services.settings.server", "");
user_pref("sidebar.backupState", "{\"width\":\"\",\"command\":\"\"}");
user_pref("signon.autofillForms", false);
user_pref("signon.management.page.os-auth.optout", "MDIEEPgAAAAAAAAAAAAAAAAAAAEwFAYIKoZIhvcNAwcECPSG7CCcA2nmBAiOZYqxcyvMbA==");
user_pref("signon.rememberSignons", false);
user_pref("startup.homepage_welcome_url", "about:blank");
user_pref("toolkit.cosmeticAnimations.enabled", false);
user_pref("toolkit.shutdown.fastShutdownStage", 3);
user_pref("toolkit.startup.last_success", 1747216514);
user_pref("toolkit.startup.max_resumed_crashes", -1);
user_pref("toolkit.telemetry.cachedClientID", "c0ffeec0-ffee-c0ff-eec0-ffeec0ffeec0");
user_pref("toolkit.telemetry.cachedProfileGroupID", "decafdec-afde-cafd-ecaf-decafdecafde");
user_pref("toolkit.telemetry.enabled", false);
user_pref("toolkit.telemetry.previousBuildID", "20250112125551");
user_pref("toolkit.telemetry.reportingpolicy.firstRun", false);
user_pref("toolkit.telemetry.server", "");
user_pref("ui.systemUsesDarkTheme", 0);
user_pref("ui.use_standins_for_native_colors", true);
user_pref("webgl.forbid-software", false);
