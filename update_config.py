#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json

# 配置文件路径
CONFIG_DIR = os.path.expanduser("~/.quark_pan")
os.makedirs(CONFIG_DIR, exist_ok=True)
config_path = f'{CONFIG_DIR}/rss_config.json'

# 检查配置文件是否存在
if os.path.exists(config_path):
    try:
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保resource_submit字段存在
        if "resource_submit" not in config:
            config["resource_submit"] = {}
        
        # 更新submitters列表，添加yiso
        if "submitters" in config["resource_submit"]:
            if "yiso" not in config["resource_submit"]["submitters"]:
                config["resource_submit"]["submitters"].append("yiso")
        else:
            config["resource_submit"]["submitters"] = ["krzb", "yiso"]
        
        # 添加yiso配置
        config["resource_submit"]["yiso"] = {
            "enabled": True,
            "api_url": "https://yiso.fun/api/member/share",
            "batch_size": 1,
            "use_proxy": False
        }
        
        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        print(f"配置文件已更新: {config_path}")
    except Exception as e:
        print(f"更新配置文件失败: {e}")
else:
    print(f"配置文件不存在: {config_path}")
