"""
资源提交管理器
"""
import os
import json
import random
import sqlite3
import asyncio
from typing import List, Dict, Any, Optional, Tuple

from utils import custom_print, get_datetime
from .base_submitter import BaseSubmitter
from .db_manager import SubmitDBManager
from . import get_submitter


class ResourceSubmitManager:
    """资源提交管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化资源提交管理器

        Args:
            config: 配置信息
        """
        self._config = {
            "enabled": True,
            "auto_submit": True,
            "submitters": ["krzb", "yiso", "macyeah"],
            "submit_delay": {
                "min": 1,
                "max": 5
            },
            "avoid_duplicate": True,  # 避免重复提交
            "krzb": {
                "enabled": True,
                "api_url": "https://fc-resource-node-api.krzb.net",
                "batch_size": 4,
                "use_proxy": False
            },
            "yiso": {
                "enabled": True,
                "api_url": "https://yiso.fun/api/member/share",
                "batch_size": 1,
                "use_proxy": False
            },
            "macyeah": {
                "enabled": True,
                "login_url": "https://154.21.90.33/login",
                "submit_url": "https://154.21.90.33/feedback",
                "batch_size": 8,
                "use_proxy": False,
                "timeout": 60,
                "retry_times": 3,
                "retry_interval": 5,
                "login_credentials": {
                    "email": "<EMAIL>",
                    "password": "albert4417"
                }
            }
        }

        # 更新配置
        if config:
            self.update_config(config)

        # 初始化提交器
        self._submitters: Dict[str, BaseSubmitter] = {}
        self._init_submitters()

        # 初始化数据库管理器
        self._db_manager = SubmitDBManager()

    def _init_submitters(self) -> None:
        """初始化所有提交器"""
        for submitter_type in self._config.get("submitters", []):
            try:
                # 获取提交器实例
                submitter = get_submitter(submitter_type)

                # 设置提交器配置
                if submitter_type in self._config:
                    submitter.set_config(self._config[submitter_type])

                # 添加到提交器字典
                self._submitters[submitter_type] = submitter

                custom_print(f"已初始化提交器: {submitter.name}")
            except Exception as e:
                custom_print(f"初始化提交器 {submitter_type} 失败: {e}", error_msg=True)

    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置

        Args:
            config: 新的配置信息
        """
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value

        # 重新初始化提交器
        self._init_submitters()

    async def submit_links(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交分享链接到所有启用的提交器

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "资源提交功能已禁用"}

        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 去重
        share_links = list(set(share_links))

        results = {}

        for submitter_type, submitter in self._submitters.items():
            try:
                # 检查是否需要避免重复提交
                if self._config.get("avoid_duplicate", True):
                    # 过滤出未提交的链接
                    unsubmitted_links = self._db_manager.get_unsubmitted_urls(share_links, submitter_type)

                    if not unsubmitted_links:
                        custom_print(f"所有链接已经提交到 {submitter.name}，跳过提交")
                        results[submitter_type] = {
                            "code": 0,
                            "message": "所有链接已经提交，跳过",
                            "skipped": True
                        }
                        continue

                    custom_print(f"找到 {len(unsubmitted_links)}/{len(share_links)} 个未提交到 {submitter.name} 的链接")
                    links_to_submit = unsubmitted_links
                else:
                    links_to_submit = share_links

                # 随机延迟
                delay_min = self._config["submit_delay"]["min"]
                delay_max = self._config["submit_delay"]["max"]
                delay = delay_min if delay_min == delay_max else random.uniform(delay_min, delay_max)

                custom_print(f"等待 {delay:.2f} 秒后使用 {submitter.name} 提交资源...")
                await asyncio.sleep(delay)

                # 提交资源
                result = await submitter.submit(links_to_submit)
                results[submitter_type] = result

                custom_print(f"{submitter.name} 提交结果: {result['message']}")

                # 保存提交记录
                if result.get("code") == 0:
                    # 获取每批次的详细信息
                    batch_results = result.get("details", [])
                    for batch_result in batch_results:
                        batch_links = batch_result.get("links", [])
                        batch_code = batch_result.get("code", -1)
                        batch_response = batch_result.get("response", {})

                        # 保存每个链接的提交记录
                        for link in batch_links:
                            self._db_manager.save_submit_record(
                                link, submitter_type,
                                status=1 if batch_code == 0 else 0,
                                response=batch_response
                            )
            except Exception as e:
                custom_print(f"{submitter.name} 提交失败: {e}", error_msg=True)
                results[submitter_type] = {
                    "code": -1,
                    "message": f"提交异常: {e}"
                }

        # 汇总结果
        success_count = sum(1 for r in results.values() if r.get("code") == 0)
        total_count = len(results)

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"成功提交到 {success_count}/{total_count} 个站点",
            "details": results
        }

    async def submit_from_database(self, limit: int = 50) -> Dict[str, Any]:
        """
        从数据库中查询未提交的分享记录并提交

        Args:
            limit: 最大查询记录数

        Returns:
            Dict: 提交结果
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "资源提交功能已禁用"}

        # 获取启用的提交器类型
        submitter_types = list(self._submitters.keys())
        if not submitter_types:
            return {"code": -1, "message": "没有启用的提交器"}

        # 获取所有未提交的链接
        unsubmitted_urls_by_type = self._db_manager.get_all_unsubmitted_urls(submitter_types, limit)

        # 统计每个站点的未提交链接数量
        unsubmitted_counts = {submitter_type: len(urls) for submitter_type, urls in unsubmitted_urls_by_type.items()}
        custom_print("=" * 50)
        custom_print("各站点未提交链接数量统计:")
        for submitter_type, count in unsubmitted_counts.items():
            submitter = self._submitters.get(submitter_type)
            submitter_name = submitter.name if submitter else submitter_type

            # 根据提交器类型显示更友好的名称
            if submitter_type == "sroad":
                display_name = "Sroad站点"
            elif submitter_type == "buyutu":
                display_name = "步游兔站点"
            elif submitter_type == "yiso":
                display_name = "一搜站点"
            elif submitter_type == "krzb":
                display_name = "快融站点"
            else:
                display_name = submitter_name

            custom_print(f"- {display_name}: {count} 条")
        custom_print("=" * 50)

        if not any(unsubmitted_urls_by_type.values()):
            return {
                "code": 0,
                "message": "没有未提交的分享记录",
                "unsubmitted_counts": unsubmitted_counts
            }

        # 统计信息
        total_links = 0
        total_submitted = 0
        results_by_type = {}

        # 对每个提交器类型，提交未提交的链接
        for submitter_type, unsubmitted_urls in unsubmitted_urls_by_type.items():
            if not unsubmitted_urls:
                custom_print(f"没有未提交到 {submitter_type} 的链接")
                continue

            total_links += len(unsubmitted_urls)
            custom_print(f"找到 {len(unsubmitted_urls)} 条未提交到 {submitter_type} 的分享记录")

            # 提交链接
            submitter = self._submitters.get(submitter_type)
            if not submitter:
                custom_print(f"找不到 {submitter_type} 提交器，跳过提交", error_msg=True)
                continue

            try:
                # 随机延迟
                delay_min = self._config["submit_delay"]["min"]
                delay_max = self._config["submit_delay"]["max"]
                delay = delay_min if delay_min == delay_max else random.uniform(delay_min, delay_max)

                custom_print(f"等待 {delay:.2f} 秒后使用 {submitter.name} 提交资源...")
                await asyncio.sleep(delay)

                # 提交资源
                result = await submitter.submit(unsubmitted_urls)
                results_by_type[submitter_type] = result

                custom_print(f"{submitter.name} 提交结果: {result['message']}")

                # 保存提交记录
                if result.get("code") == 0:
                    # 获取每批次的详细信息
                    batch_results = result.get("details", [])
                    for batch_result in batch_results:
                        batch_links = batch_result.get("links", [])
                        batch_code = batch_result.get("code", -1)
                        batch_response = batch_result.get("response", {})

                        # 保存每个链接的提交记录
                        for link in batch_links:
                            self._db_manager.save_submit_record(
                                link, submitter_type,
                                status=1 if batch_code == 0 else 0,
                                response=batch_response
                            )

                    # 统计成功提交的链接数
                    if batch_code == 0:
                        total_submitted += len(batch_links)
            except Exception as e:
                custom_print(f"{submitter.name} 提交失败: {e}", error_msg=True)
                results_by_type[submitter_type] = {
                    "code": -1,
                    "message": f"提交异常: {e}"
                }

        # 汇总结果
        success_count = sum(1 for r in results_by_type.values() if r.get("code") == 0)
        total_count = len(results_by_type)

        # 统计每个站点的未提交链接数量
        unsubmitted_counts = {submitter_type: len(urls) for submitter_type, urls in unsubmitted_urls_by_type.items()}

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"已处理 {total_links} 条分享记录，成功提交 {total_submitted} 条到 {success_count}/{total_count} 个站点",
            "details": results_by_type,
            "unsubmitted_counts": unsubmitted_counts
        }

    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return self._config

    def get_submitter(self, submitter_type: str) -> Optional[BaseSubmitter]:
        """
        获取指定类型的提交器

        Args:
            submitter_type: 提交器类型

        Returns:
            Optional[BaseSubmitter]: 提交器实例，如果不存在则返回None
        """
        return self._submitters.get(submitter_type)

    async def close(self) -> None:
        """关闭所有提交器"""
        for submitter_type, submitter in self._submitters.items():
            try:
                if hasattr(submitter, 'close') and callable(getattr(submitter, 'close')):
                    await submitter.close()
                    custom_print(f"已关闭提交器: {submitter.name}")
            except Exception as e:
                custom_print(f"关闭提交器 {submitter.name} 失败: {e}", error_msg=True)
