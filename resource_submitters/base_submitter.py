"""
资源提交器基类
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union


class BaseSubmitter(ABC):
    """资源提交器基类"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """提交器名称"""
        pass
    
    @abstractmethod
    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接
        
        Args:
            share_links: 分享链接列表
            
        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        pass
    
    @abstractmethod
    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态
        
        Args:
            submission_id: 提交ID
            
        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """
        获取提交器配置
        
        Returns:
            Dict: 提交器配置
        """
        pass
    
    @abstractmethod
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置提交器配置
        
        Args:
            config: 提交器配置
        """
        pass
