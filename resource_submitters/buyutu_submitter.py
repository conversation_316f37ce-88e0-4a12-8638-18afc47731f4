#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步游兔资源提交器
"""

import asyncio
import json
import random
import aiohttp
from typing import Dict, Any, List
from fake_useragent import UserAgent

from .base_submitter import BaseSubmitter
from utils import custom_print


class BuyutuSubmitter(BaseSubmitter):
    """步游兔资源提交器"""

    @property
    def name(self) -> str:
        """提交器名称"""
        return "步游兔"

    def __init__(self):
        """初始化步游兔资源提交器"""
        self._config = {
            "enabled": True,
            "api_url": "https://www.buyutu.com/sub",
            "batch_size": 10,  # 每次提交的链接数量
            "retry_count": 3,  # 提交失败时的重试次数
            "delay": {
                "min": 1.0,  # 最小延迟时间（秒）
                "max": 3.0    # 最大延迟时间（秒）
            },
            "random_ua": True,  # 是否使用随机UA
            "use_proxy": False,
            "proxy": {
                "http": "http://127.0.0.1:7890",
                "https": "http://127.0.0.1:7890"
            },
            "headers": {
                "accept": "application/json, text/javascript, */*; q=0.01",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
        }

        # 初始化UA生成器
        self._ua_generator = UserAgent()

    def _get_random_ua(self) -> str:
        """获取随机UA"""
        try:
            return self._ua_generator.random
        except Exception:
            # 如果获取随机UA失败，则使用默认UA
            return self._config["headers"]["User-Agent"]

    async def _get_client(self) -> aiohttp.ClientSession:
        """获取HTTP客户端"""
        # 创建客户端会话
        return aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(ssl=False),
            trust_env=True,
            timeout=aiohttp.ClientTimeout(total=60.0),
            headers=self._config["headers"]
        )

    async def submit_single_batch(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交一批分享链接到步游兔

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果
        """
        if not share_links:
            return {
                "code": -1,
                "message": "没有可提交的链接",
                "links": []
            }

        # 获取重试次数
        retry_count = self._config["retry_count"]

        for attempt in range(retry_count + 1):  # +1是因为第一次不算重试
            try:
                # 每次尝试都使用新的客户端和随机UA
                client = await self._get_client()

                # 应用随机UA
                if self._config["random_ua"]:
                    headers = self._config["headers"].copy()
                    headers["User-Agent"] = self._get_random_ua()
                else:
                    headers = self._config["headers"]

                # 记录尝试次数
                attempt_msg = f"(尝试 {attempt + 1}/{retry_count + 1})"
                custom_print(f"正在提交 {len(share_links)} 个链接到步游兔 {attempt_msg}")

                # 准备请求数据
                # 将链接列表转换为换行符分隔的字符串
                links_str = "\r\n".join(share_links)

                # 构建请求数据
                data = {
                    "d": links_str
                }

                # 发送请求
                response = await client.post(
                    self._config["api_url"],
                    headers=headers,
                    data=data,
                    timeout=60.0
                )

                # 关闭客户端
                await client.close()

                # 检查响应状态
                if response.status == 200:
                    try:
                        # 获取原始响应文本
                        raw_text = await response.text()
                        custom_print(f"步游兔原始响应: {raw_text}")

                        # 尝试解析JSON响应
                        try:
                            json_data = json.loads(raw_text)
                        except json.JSONDecodeError:
                            # 如果不是有效的JSON，使用原始文本
                            json_data = {"raw_text": raw_text}

                        # 检查提交结果
                        if json_data.get("code") == 200 or json_data.get("status") == 200:
                            # 提交成功
                            custom_print(f"成功提交 {len(share_links)} 个链接到步游兔")

                            # 记录详细的服务器响应信息
                            server_response = {
                                "code": json_data.get("code", json_data.get("status", 200)),
                                "message": json_data.get("msg", json_data.get("message", "提交成功")),
                                "raw_response": json_data,
                                "raw_text": raw_text
                            }

                            return {
                                "code": 0,
                                "message": "提交成功",
                                "links": share_links,
                                "response": server_response
                            }
                        else:
                            # 提取错误信息
                            error_msg = json_data.get("msg", json_data.get("message", "未知错误"))

                            # 检查是否是重复提交
                            duplicate_indicators = ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]
                            is_duplicate = any(indicator in error_msg.lower() for indicator in duplicate_indicators)

                            # 记录详细的服务器响应信息
                            server_response = {
                                "code": json_data.get("code", json_data.get("status", -1)),
                                "message": error_msg,
                                "raw_response": json_data,
                                "raw_text": raw_text,
                                "is_duplicate": is_duplicate
                            }

                            if is_duplicate:
                                custom_print(f"链接已存在于步游兔: {', '.join(share_links[:3])}...")
                                # 将重复提交视为成功
                                return {
                                    "code": 0,
                                    "message": "资源已存在(视为成功)",
                                    "links": share_links,
                                    "response": server_response
                                }
                            else:
                                custom_print(f"提交资源到步游兔失败 {attempt_msg}: {error_msg}", error_msg=True)

                                # 如果还有重试次数，则继续重试
                                if attempt < retry_count:
                                    delay = random.uniform(
                                        self._config["delay"]["min"],
                                        self._config["delay"]["max"]
                                    )
                                    custom_print(f"将在 {delay:.2f} 秒后重试...")
                                    await asyncio.sleep(delay)
                                    continue

                                return {
                                    "code": -1,
                                    "message": error_msg,
                                    "links": share_links,
                                    "response": server_response
                                }
                    except Exception as e:
                        # 处理其他异常（不应该发生，因为我们已经读取了响应文本）
                        error_msg = f"处理响应时出错: {e}"
                        custom_print(f"提交资源到步游兔失败 {attempt_msg}: {error_msg}", error_msg=True)

                        # 记录详细的服务器响应信息
                        server_response = {
                            "code": response.status,
                            "message": error_msg,
                            "raw_text": raw_text,
                            "error": str(e)
                        }

                        # 如果还有重试次数，则继续重试
                        if attempt < retry_count:
                            delay = random.uniform(
                                self._config["delay"]["min"],
                                self._config["delay"]["max"]
                            )
                            custom_print(f"将在 {delay:.2f} 秒后重试...")
                            await asyncio.sleep(delay)
                            continue

                        return {
                            "code": -1,
                            "message": error_msg,
                            "links": share_links,
                            "response": server_response
                        }
                else:
                    # 响应状态码不是200
                    error_msg = f"响应状态码: {response.status}"
                    custom_print(f"提交资源到步游兔失败 {attempt_msg}: {error_msg}", error_msg=True)

                    # 尝试获取原始响应文本
                    try:
                        raw_text = await response.text()
                        custom_print(f"步游兔错误响应: {raw_text}")
                    except Exception as e:
                        raw_text = f"无法获取响应内容: {e}"

                    # 记录详细的服务器响应信息
                    server_response = {
                        "code": response.status,
                        "message": error_msg,
                        "raw_text": raw_text
                    }

                    # 如果还有重试次数，则继续重试
                    if attempt < retry_count:
                        delay = random.uniform(
                            self._config["delay"]["min"],
                            self._config["delay"]["max"]
                        )
                        custom_print(f"将在 {delay:.2f} 秒后重试...")
                        await asyncio.sleep(delay)
                        continue

                    return {
                        "code": -1,
                        "message": error_msg,
                        "links": share_links,
                        "response": server_response
                    }
            except Exception as e:
                # 请求异常
                error_msg = f"请求异常: {e}"
                custom_print(f"提交资源到步游兔失败 {attempt_msg}: {error_msg}", error_msg=True)

                # 记录详细的错误信息
                server_response = {
                    "code": -1,
                    "message": error_msg,
                    "error": str(e),
                    "exception_type": type(e).__name__
                }

                # 如果还有重试次数，则继续重试
                if attempt < retry_count:
                    delay = random.uniform(
                        self._config["delay"]["min"],
                        self._config["delay"]["max"]
                    )
                    custom_print(f"将在 {delay:.2f} 秒后重试...")
                    await asyncio.sleep(delay)
                    continue

                return {
                    "code": -1,
                    "message": error_msg,
                    "links": share_links,
                    "response": server_response
                }

        # 所有重试都失败
        server_response = {
            "code": -1,
            "message": "所有重试都失败",
            "error": "达到最大重试次数",
            "retry_count": self._config["retry_count"]
        }

        return {
            "code": -1,
            "message": "所有重试都失败",
            "links": share_links,
            "response": server_response
        }

    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 去重
        share_links = list(set(share_links))

        # 分批提交
        batch_size = self._config["batch_size"]
        results = []

        for i in range(0, len(share_links), batch_size):
            batch_links = share_links[i:i+batch_size]

            # 提交一批链接
            result = await self.submit_single_batch(batch_links)
            results.append(result)

            # 随机延迟1-3秒，避免请求过快
            if i + batch_size < len(share_links):
                delay = random.uniform(
                    self._config["delay"]["min"],
                    self._config["delay"]["max"]
                )
                custom_print(f"等待 {delay:.2f} 秒后提交下一批链接...")
                await asyncio.sleep(delay)

        # 汇总结果
        success_count = sum(1 for r in results if r["code"] == 0)
        duplicate_count = sum(1 for r in results if r["code"] == 0 and "已存在" in r["message"])
        new_success_count = success_count - duplicate_count
        total_count = len(results)

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"成功提交 {success_count}/{total_count} 批资源到步游兔 (新增: {new_success_count}, 已存在: {duplicate_count})",
            "details": results
        }

    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态

        Args:
            submission_id: 提交ID

        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        # 步游兔不支持检查提交状态
        # 忽略submission_id参数，返回固定结果
        _ = submission_id  # 使用变量避免未使用警告

        return {
            "code": 0,
            "message": "步游兔不支持检查提交状态",
            "details": {
                "supported": False,
                "reason": "步游兔API不提供提交状态查询功能"
            }
        }

    def get_config(self) -> Dict[str, Any]:
        """
        获取提交器配置

        Returns:
            Dict: 提交器配置
        """
        return self._config

    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置提交器配置

        Args:
            config: 提交器配置
        """
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value
