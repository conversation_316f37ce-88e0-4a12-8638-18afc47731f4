"""
马克耶网盘资源提交器
"""
import re
import base64
import json
import asyncio
import httpx
from typing import List, Dict, Any, Optional, Union
from urllib.parse import quote

from utils import custom_print
from .base_submitter import BaseSubmitter


class MacyeahSubmitter(BaseSubmitter):
    """马克耶网盘资源提交器"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化马克耶网盘资源提交器

        Args:
            config: 配置信息
        """
        # 默认配置
        self._config = {
            "enabled": True,
            "name": "马克耶网盘",
            "login_url": "https://154.21.90.33/login",
            "submit_url": "https://154.21.90.33/feedback",
            "batch_size": 8,
            "use_proxy": False,
            "timeout": 60,
            "retry_times": 3,
            "retry_interval": 5,
            "verify_ssl": False,  # 禁用SSL证书验证
            "login_credentials": {
                "email": "<EMAIL>",
                "password": "albert4417"
            },
            "common_headers": {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
            },
            "cookies": {}
        }

        # 更新配置
        if config:
            self.set_config(config)

        # 登录状态
        self._logged_in = False
        self._token = None
        self._client = None

    @property
    def name(self) -> str:
        """提交器名称"""
        return self._config["name"]

    def get_config(self) -> Dict[str, Any]:
        """
        获取提交器配置

        Returns:
            Dict: 提交器配置
        """
        return self._config

    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置提交器配置

        Args:
            config: 提交器配置
        """
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value

    async def _create_client(self) -> httpx.AsyncClient:
        """
        创建新的HTTP客户端

        Returns:
            httpx.AsyncClient: HTTP客户端
        """
        # 代理配置
        proxies = None
        if self._config["use_proxy"]:
            from proxy_manager import get_proxy
            proxy = await get_proxy()
            if proxy:
                proxies = {
                    "http://": f"http://{proxy}",
                    "https://": f"http://{proxy}"
                }

        # 创建客户端，不预设cookies
        client = httpx.AsyncClient(
            proxies=proxies,
            follow_redirects=True,
            timeout=self._config["timeout"],
            verify=self._config.get("verify_ssl", True)  # 使用配置中的SSL验证设置
        )

        # 从配置中获取cookies并设置
        config_cookies = self._config.get("cookies", {})
        cookie_count = 0

        # 只设置有效的cookies
        for name, value in config_cookies.items():
            if value:  # 只设置非空值的cookies
                client.cookies.set(name, value)
                cookie_count += 1

        custom_print(f"已创建新的HTTP客户端，设置了 {cookie_count} 个cookies")
        return client

    def _update_cookies(self, client: httpx.AsyncClient) -> None:
        """
        更新配置中的cookies

        Args:
            client: HTTP客户端
        """
        # 清除旧的cookies
        self._config["cookies"] = {}

        # 获取新的cookies，使用更安全的方式
        try:
            # 遍历cookies jar中的所有cookies
            for cookie in client.cookies.jar:
                # 只保留最新的cookie值
                self._config["cookies"][cookie.name] = cookie.value

            custom_print(f"已更新cookies，共 {len(self._config['cookies'])} 个")

            # 打印cookie名称以便调试
            cookie_names = list(self._config["cookies"].keys())
            custom_print(f"Cookie名称: {cookie_names}")

        except Exception as e:
            custom_print(f"更新cookies时出错: {e}", error_msg=True)
            # 如果出错，尝试使用备用方法
            try:
                new_cookies = {}
                for name, value in client.cookies.items():
                    new_cookies[name] = value
                self._config["cookies"] = new_cookies
                custom_print(f"使用备用方法更新cookies，共 {len(new_cookies)} 个")
            except Exception as e2:
                custom_print(f"备用方法也失败: {e2}", error_msg=True)

    async def close(self) -> None:
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def _check_login_status(self, force_check: bool = False) -> bool:
        """
        检查登录状态

        Args:
            force_check: 是否强制检查

        Returns:
            bool: 是否已登录
        """
        if self._logged_in and not force_check:
            return True

        try:
            # 创建新的客户端
            client = await self._create_client()

            # 访问首页，检查是否已登录
            response = await client.get(
                "https://154.21.90.33/",
                headers=self._config["common_headers"],
                timeout=self._config["timeout"]
            )

            # 检查响应内容，判断是否已登录
            response_text = response.text

            # 检查是否已登录
            # 如果访问首页返回200且包含正常内容，说明已登录
            if response.status_code == 200:
                # 检查是否包含登录页面的标志（如果包含说明未登录）
                login_page_indicators = ["登录", "login", "email", "password", "_token"]
                is_login_page = any(indicator in response_text.lower() for indicator in login_page_indicators)

                if not is_login_page:
                    # 不是登录页面，检查是否包含正常首页内容
                    home_page_indicators = ["马克耶网盘搜索", "搜索", "feedback", "反馈"]
                    is_home_page = any(indicator in response_text for indicator in home_page_indicators)

                    if is_home_page:
                        self._logged_in = True
                        # 更新cookies
                        self._update_cookies(client)
                        await client.aclose()
                        custom_print("检查登录状态：已登录")
                        return True

            self._logged_in = False
            await client.aclose()
            custom_print("检查登录状态：未登录")
            return False
        except Exception as e:
            custom_print(f"检查登录状态出错: {e}", error_msg=True)
            self._logged_in = False
            return False

    async def _get_token(self, url: str) -> Optional[str]:
        """
        从页面获取CSRF令牌

        Args:
            url: 页面URL

        Returns:
            Optional[str]: CSRF令牌
        """
        try:
            # 创建新的客户端
            client = await self._create_client()

            # 访问页面
            response = await client.get(
                url,
                headers=self._config["common_headers"],
                timeout=self._config["timeout"]
            )

            # 解析响应内容
            response_text = response.text

            # 使用正则表达式提取令牌
            token_match = re.search(r'name="_token"\s+value="([^"]+)"', response_text)
            if token_match:
                token = token_match.group(1)
                custom_print(f"成功获取CSRF令牌: {token[:10]}...")

                # 更新cookies
                self._update_cookies(client)
                await client.aclose()
                return token

            # 如果没有找到令牌，打印部分响应内容以便调试
            content_preview = response.text[:200] + "..." if len(response.text) > 200 else response.text
            custom_print(f"响应内容不包含令牌: {content_preview}", error_msg=True)
            await client.aclose()
            return None
        except Exception as e:
            custom_print(f"获取令牌出错: {e}", error_msg=True)
            return None

    async def login(self) -> bool:
        """
        登录马克耶网盘

        Returns:
            bool: 是否登录成功
        """
        # 先检查是否已登录
        if await self._check_login_status():
            custom_print("已经登录马克耶网盘，无需重复登录")
            return True

        custom_print("开始登录马克耶网盘...")

        # 清除登录状态和cookies
        self._logged_in = False
        self._config["cookies"] = {}

        # 最大重试次数
        max_retries = self._config["retry_times"]

        for retry in range(max_retries):
            try:
                # 获取登录页面的CSRF令牌
                token = await self._get_token(self._config["login_url"])
                if not token:
                    custom_print("无法获取登录令牌", error_msg=True)
                    continue

                client = await self._create_client()

                # 构建登录表单数据
                login_data = {
                    "_token": token,
                    "email": self._config["login_credentials"]["email"],
                    "password": self._config["login_credentials"]["password"]
                }

                # 发送登录请求
                login_url = self._config["login_url"]
                custom_print(f"正在发送登录请求: {login_url} (尝试 {retry+1}/{max_retries})")

                response = await client.post(
                    login_url,
                    data=login_data,
                    headers=self._config["common_headers"],
                    timeout=self._config["timeout"],
                    follow_redirects=True
                )

                # 检查登录是否成功
                custom_print(f"登录响应状态码: {response.status_code}")
                custom_print(f"登录响应URL: {response.url}")

                # 更新cookies，无论登录是否成功都要保存cookies
                self._update_cookies(client)

                # 根据HAR文件分析，登录成功的标志是：
                # 1. 状态码为302（重定向）或200
                # 2. 重定向到首页 https://154.21.90.33 或者响应URL是首页
                login_success = False

                if response.status_code == 302:
                    # 302重定向，检查Location头
                    location = response.headers.get('location', '')
                    if location == "https://154.21.90.33" or location == "https://154.21.90.33/":
                        login_success = True
                        custom_print("检测到302重定向到首页，登录成功")
                elif response.status_code == 200:
                    # 200响应，检查URL是否是首页
                    if str(response.url) in ["https://154.21.90.33", "https://154.21.90.33/"]:
                        login_success = True
                        custom_print("检测到200响应且URL为首页，登录成功")
                    else:
                        # 检查响应内容中是否包含登录成功的标志
                        response_text = response.text

                        # 检查是否包含登录失败的标志
                        error_indicators = ["登录失败", "用户名或密码错误", "invalid", "error", "密码错误"]
                        has_error = any(indicator in response_text.lower() for indicator in error_indicators)

                        if not has_error:
                            # 如果没有错误标志，且响应内容较短，可能是成功
                            if len(response_text) < 1000:
                                login_success = True
                                custom_print("响应内容较短且无错误标志，认为登录成功")
                            else:
                                # 检查是否包含成功标志
                                success_indicators = ["马克耶网盘搜索", "搜索", "feedback", "反馈"]
                                if any(indicator in response_text for indicator in success_indicators):
                                    login_success = True
                                    custom_print("检测到成功标志，登录成功")

                if login_success:
                    self._logged_in = True
                    await client.aclose()
                    custom_print("登录马克耶网盘成功")
                    return True
                else:
                    # 打印调试信息
                    response_text = response.text
                    content_preview = response_text[:500] + "..." if len(response_text) > 500 else response_text
                    custom_print(f"登录失败，响应内容: {content_preview}", error_msg=True)
                    await client.aclose()

            except Exception as e:
                custom_print(f"登录出错: {e}", error_msg=True)

            # 等待一段时间后重试
            if retry < max_retries - 1:
                await asyncio.sleep(self._config["retry_interval"])

        custom_print("登录马克耶网盘失败，已达到最大重试次数", error_msg=True)
        return False

    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "提交器已禁用"}

        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 检查登录状态，强制检查
        login_status = await self._check_login_status(force_check=True)
        if not login_status:
            custom_print("未登录或登录已过期，尝试登录")
            login_success = await self.login()
            if not login_success:
                return {"code": -1, "message": "登录马克耶网盘失败，无法提交资源"}

        # 分批提交
        batch_size = self._config["batch_size"]
        results = []

        for i in range(0, len(share_links), batch_size):
            batch_links = share_links[i:i+batch_size]

            # 获取提交页面的CSRF令牌
            token = await self._get_token(self._config["submit_url"])
            if not token:
                custom_print("无法获取提交令牌", error_msg=True)
                continue

            # 构建请求数据
            links_str = "\n".join(batch_links)
            data = {
                "_token": token,
                "feedback_info": links_str
            }

            # 构建请求URL
            api_url = self._config["submit_url"]

            # 构建请求头
            headers = self._config["common_headers"].copy()
            headers["Content-Type"] = "application/x-www-form-urlencoded"
            headers["Referer"] = self._config["submit_url"]

            try:
                client = await self._create_client()

                # 发送提交请求
                custom_print(f"正在提交第 {i//batch_size + 1} 批资源 ({len(batch_links)} 个链接)")

                response = await client.post(
                    api_url,
                    data=data,
                    headers=headers,
                    timeout=self._config["timeout"]
                )

                # 检查提交是否成功
                if response.status_code == 200 or response.status_code == 302:
                    if "反馈成功" in response.text:
                        custom_print(f"第 {i//batch_size + 1} 批资源提交成功")
                        results.append({
                            "code": 0,
                            "message": "提交成功",
                            "links": batch_links,
                            "response": {"status": "success", "text": "反馈成功"}
                        })
                    else:
                        custom_print(f"第 {i//batch_size + 1} 批资源提交失败，响应内容不包含成功标识", error_msg=True)
                        results.append({
                            "code": -1,
                            "message": "提交失败，响应内容不包含成功标识",
                            "links": batch_links,
                            "response": {"status": "failed", "text": response.text[:200]}
                        })
                else:
                    custom_print(f"第 {i//batch_size + 1} 批资源提交失败，状态码: {response.status_code}", error_msg=True)
                    results.append({
                        "code": -1,
                        "message": f"提交失败，状态码: {response.status_code}",
                        "links": batch_links,
                        "response": {"status": "failed", "status_code": response.status_code}
                    })

                # 关闭客户端
                await client.aclose()

                # 等待一段时间，避免频繁请求
                await asyncio.sleep(1)

            except Exception as e:
                custom_print(f"提交资源时出错: {e}", error_msg=True)
                results.append({
                    "code": -1,
                    "message": f"提交出错: {str(e)}",
                    "links": batch_links,
                    "response": {"status": "error", "error": str(e)}
                })

                # 检查是否需要重新登录
                if "登录" in str(e) or "认证" in str(e):
                    custom_print("提交资源时发现需要登录，尝试重新登录", error_msg=True)

                    # 清除登录状态和cookies
                    self._logged_in = False
                    self._config["cookies"] = {}

                    # 尝试重新登录
                    login_success = await self.login()
                    if not login_success:
                        return {"code": -1, "message": "重新登录失败，无法继续提交资源"}

        # 汇总结果
        success_count = sum(1 for r in results if r["code"] == 0)
        total_count = len(results)

        if success_count > 0:
            return {
                "code": 0,
                "message": f"成功提交 {success_count}/{total_count} 批资源",
                "details": results
            }
        else:
            return {
                "code": -1,
                "message": "所有资源提交失败",
                "details": results
            }

    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态

        Args:
            submission_id: 提交ID

        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        # 马克耶网盘不支持检查提交状态
        return {"code": -1, "message": "不支持检查提交状态"}
