"""
易搜资源提交器
"""
import json
import random
import asyncio
import time
from typing import List, Dict, Any, Union

import httpx

from utils import custom_print
from .base_submitter import BaseSubmitter


class YisoSubmitter(BaseSubmitter):
    """易搜资源提交器"""

    def __init__(self):
        """初始化易搜资源提交器"""
        self._config = {
            "enabled": True,
            "api_url": "https://yiso.fun/api/member/share",
            "batch_size": 1,  # 每次提交的链接数量，易搜只支持单个链接提交
            "retry_count": 3,  # 提交失败时的重试次数
            "delay": {
                "min": 1.0,  # 最小延迟时间（秒）
                "max": 3.0    # 最大延迟时间（秒）
            },
            "random_ua": True,  # 是否使用随机UA
            "use_proxy": False,
            "proxy": {
                "http": "http://127.0.0.1:7890",
                "https": "http://127.0.0.1:7890"
            },
            "headers": {
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json",
                "Referer": "https://yiso.fun/",
                "Origin": "https://yiso.fun"
            },
            "user_agents": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0"
            ]
        }

        # 会话对象
        self._client = None

    @property
    def name(self) -> str:
        """提交器名称"""
        return "易搜资源提交器"

    def get_config(self) -> Dict[str, Any]:
        """获取提交器配置"""
        return self._config

    def set_config(self, config: Dict[str, Any]) -> None:
        """设置提交器配置"""
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value

    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self._client is not None:
            await self._client.aclose()

        if self._config["use_proxy"]:
            proxies = {
                "http://": self._config["proxy"]["http"],
                "https://": self._config["proxy"]["https"]
            }
            self._client = httpx.AsyncClient(proxies=proxies)
        else:
            self._client = httpx.AsyncClient()

        return self._client
        
    def _get_random_ua(self) -> str:
        """获取随机User-Agent"""
        if not self._config["random_ua"]:
            return self._config["headers"]["User-Agent"]
            
        return random.choice(self._config["user_agents"])
        
    async def _submit_with_retry(self, link: str, retry_count: int = None) -> Dict[str, Any]:
        """
        提交单个链接并支持重试
        
        Args:
            link: 分享链接
            retry_count: 重试次数，若为None则使用配置中的值
            
        Returns:
            Dict: 提交结果
        """
        if retry_count is None:
            retry_count = self._config["retry_count"]
            
        for attempt in range(retry_count + 1):  # +1是因为第一次不算重试
            try:
                # 每次尝试都使用新的客户端和随机UA
                client = await self._get_client()
                
                # 应用随机UA
                if self._config["random_ua"]:
                    headers = self._config["headers"].copy()
                    headers["User-Agent"] = self._get_random_ua()
                else:
                    headers = self._config["headers"]
                
                # 构建请求URL
                api_url = f"{self._config['api_url']}?url={link}"
                
                # 记录尝试次数
                attempt_msg = f"(尝试 {attempt + 1}/{retry_count + 1})"
                custom_print(f"正在提交链接到易搜 {attempt_msg}: {link}")
                
                # 发送请求
                response = await client.get(
                    api_url,
                    headers=headers,
                    timeout=60.0
                )
                
                # 检查响应状态码
                if response.status_code != 200:
                    error_msg = f"HTTP错误: {response.status_code}"
                    custom_print(f"提交资源失败 {attempt_msg}: {error_msg}", error_msg=True)
                    
                    # 如果还有重试次数，则继续重试
                    if attempt < retry_count:
                        delay = random.uniform(
                            self._config["delay"]["min"],
                            self._config["delay"]["max"]
                        )
                        custom_print(f"将在 {delay:.2f} 秒后重试...")
                        await asyncio.sleep(delay)
                        continue
                        
                    return {
                        "code": response.status_code,
                        "message": error_msg,
                        "links": [link]
                    }
                
                # 解析响应
                try:
                    json_data = response.json()
                    custom_print(f"易搜响应 {attempt_msg}: {json_data}")
                    
                    # 检查响应是否成功
                    if json_data.get("code") == 200 and json_data.get("msg") == "SUCCESS":
                        custom_print(f"成功提交链接到易搜: {link}")
                        return {
                            "code": 0,
                            "message": "提交成功",
                            "links": [link],
                            "response": json_data
                        }
                    else:
                        # 提取错误信息
                        error_msg = json_data.get("msg", "未知错误")
                        
                        # 检查是否是重复提交
                        duplicate_indicators = ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]
                        is_duplicate = any(indicator in error_msg for indicator in duplicate_indicators)
                        
                        if is_duplicate:
                            custom_print(f"链接已存在于易搜: {link}")
                            # 将重复提交视为成功
                            return {
                                "code": 0,
                                "message": "资源已存在(视为成功)",
                                "links": [link],
                                "response": json_data
                            }
                        else:
                            custom_print(f"提交资源到易搜失败 {attempt_msg}: {error_msg}", error_msg=True)
                            
                            # 如果还有重试次数，则继续重试
                            if attempt < retry_count:
                                delay = random.uniform(
                                    self._config["delay"]["min"],
                                    self._config["delay"]["max"]
                                )
                                custom_print(f"将在 {delay:.2f} 秒后重试...")
                                await asyncio.sleep(delay)
                                continue
                                
                            return {
                                "code": -1,
                                "message": error_msg,
                                "links": [link],
                                "response": json_data
                            }
                except json.JSONDecodeError:
                    # 响应不是JSON格式
                    error_msg = "响应不是JSON格式"
                    custom_print(f"提交资源到易搜失败 {attempt_msg}: {error_msg}", error_msg=True)
                    
                    # 如果还有重试次数，则继续重试
                    if attempt < retry_count:
                        delay = random.uniform(
                            self._config["delay"]["min"],
                            self._config["delay"]["max"]
                        )
                        custom_print(f"将在 {delay:.2f} 秒后重试...")
                        await asyncio.sleep(delay)
                        continue
                        
                    return {
                        "code": -1,
                        "message": error_msg,
                        "links": [link],
                        "response": {"raw_response": response.text}
                    }
                    
            except Exception as e:
                error_msg = f"请求失败: {e}"
                custom_print(f"提交资源到易搜请求失败 {attempt_msg}: {error_msg}", error_msg=True)
                
                # 如果还有重试次数，则继续重试
                if attempt < retry_count:
                    delay = random.uniform(
                        self._config["delay"]["min"],
                        self._config["delay"]["max"]
                    )
                    custom_print(f"将在 {delay:.2f} 秒后重试...")
                    await asyncio.sleep(delay)
                    continue
                    
                return {
                    "code": -1,
                    "message": error_msg,
                    "links": [link]
                }
                
        # 这里不应该到达，但为了安全起见添加
        return {
            "code": -1,
            "message": "所有重试都失败",
            "links": [link]
        }

    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "提交器已禁用"}

        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 分批提交
        batch_size = self._config["batch_size"]
        results = []

        for i in range(0, len(share_links), batch_size):
            batch_links = share_links[i:i+batch_size]

            for link in batch_links:
                try:
                    client = await self._get_client()

                    # 构建请求URL
                    api_url = f"{self._config['api_url']}?url={link}"

                    # 发送请求
                    custom_print(f"正在提交链接到易搜: {link}")
                    response = await client.get(
                        api_url,
                        headers=self._config["headers"],
                        timeout=60.0
                    )

                    # 检查响应状态码
                    if response.status_code != 200:
                        custom_print(f"提交资源失败: HTTP状态码 {response.status_code}", error_msg=True)
                        results.append({
                            "code": response.status_code,
                            "message": f"HTTP错误: {response.status_code}",
                            "links": [link]
                        })
                        continue

                    # 解析响应
                    try:
                        json_data = response.json()
                        custom_print(f"易搜响应: {json_data}")

                        # 检查响应是否成功
                        if json_data.get("code") == 200 and json_data.get("msg") == "SUCCESS":
                            custom_print(f"成功提交链接到易搜: {link}")
                            results.append({
                                "code": 0,
                                "message": "提交成功",
                                "links": [link],
                                "response": json_data
                            })
                        else:
                            # 提取错误信息
                            error_msg = json_data.get("msg", "未知错误")
                            
                            # 检查是否是重复提交
                            duplicate_indicators = ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]
                            is_duplicate = any(indicator in error_msg for indicator in duplicate_indicators)

                            if is_duplicate:
                                custom_print(f"链接已存在于易搜: {link}")
                                # 将重复提交视为成功
                                results.append({
                                    "code": 0,
                                    "message": "资源已存在(视为成功)",
                                    "links": [link],
                                    "response": json_data
                                })
                            else:
                                custom_print(f"提交资源到易搜失败: {error_msg}", error_msg=True)
                                results.append({
                                    "code": -1,
                                    "message": error_msg,
                                    "links": [link],
                                    "response": json_data
                                })
                    except json.JSONDecodeError:
                        # 响应不是JSON格式
                        custom_print(f"提交资源到易搜失败: 响应不是JSON格式", error_msg=True)
                        results.append({
                            "code": -1,
                            "message": "响应不是JSON格式",
                            "links": [link],
                            "response": {"raw_response": response.text}
                        })
                except Exception as e:
                    custom_print(f"提交资源到易搜请求失败: {e}", error_msg=True)
                    results.append({
                        "code": -1,
                        "message": f"请求失败: {e}",
                        "links": [link]
                    })

                # 随机延迟1-3秒，避免请求过快
                if i + batch_size < len(share_links) or (i < len(share_links) and link != batch_links[-1]):
                    delay = random.uniform(1, 3)
                    custom_print(f"等待 {delay:.2f} 秒后提交下一个链接...")
                    await asyncio.sleep(delay)

        # 汇总结果
        success_count = sum(1 for r in results if r["code"] == 0)
        duplicate_count = sum(1 for r in results if r["code"] == 0 and "已存在" in r["message"])
        new_success_count = success_count - duplicate_count
        total_count = len(results)

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"成功提交 {success_count}/{total_count} 个资源到易搜 (新增: {new_success_count}, 已存在: {duplicate_count})",
            "details": results
        }

    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态 (易搜API不支持此功能)

        Args:
            submission_id: 提交ID

        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        return {
            "code": -1,
            "message": "易搜API不支持检查提交状态"
        }

    async def close(self) -> None:
        """关闭HTTP客户端"""
        if self._client is not None:
            await self._client.aclose()
            self._client = None
