"""
云搜网资源提交器 - 新版
"""
import json
import random
import asyncio
import base64
import re
import time
from typing import List, Dict, Any, Optional

import httpx

from utils import custom_print
from .base_submitter import BaseSubmitter


class YunsoSubmitter(BaseSubmitter):
    """云搜网资源提交器"""

    def __init__(self):
        """初始化云搜网资源提交器"""
        self._config = {
            "enabled": True,
            "api_url": "https://www.yunso.net/api/validate/dataentry.html",
            "login_url": "https://dash.yunso.net/index/user/login.html",
            "dataentry_url": "https://www.yunso.net/index/user/dataentry",
            "check_login_url": "https://www.yunso.net/index/user/index.html",  # 用于检查登录状态的URL
            "batch_size": 10,  # 每次提交的链接数量
            "use_proxy": False,
            "proxy": {
                "http": "http://127.0.0.1:7890",
                "https": "http://127.0.0.1:7890"
            },
            "common_headers": {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "DNT": "1"
            },
            "submit_headers": {
                "accept": "*/*",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "priority": "u=1, i",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest"
            },
            "login_headers": {
                "accept": "application/json, text/javascript, */*; q=0.01",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "priority": "u=1, i",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest"
            },
            "login_page_headers": {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1"
            },
            "login_credentials": {
                "url": "https://dash.yunso.net/index/user/index.html",
                "account": "albert",
                "password": "xWumeC47q9LX2LF",
                "keeplogin": "1"
            },
            "publicity": "1",  # 公开性参数
            "cookies": {},  # 存储登录后的cookies
            "auth_cookies": []  # 关键认证cookie名称列表
        }

        # 登录状态
        self._logged_in = False
        self._login_time = 0  # 上次登录时间
        self._login_valid_duration = 1800  # 登录有效期(秒)，默认30分钟

        # 会话对象
        self._client = None

    @property
    def name(self) -> str:
        """提交器名称"""
        return "云搜网资源提交器"

    def get_config(self) -> Dict[str, Any]:
        """获取提交器配置"""
        return self._config

    def set_config(self, config: Dict[str, Any]) -> None:
        """设置提交器配置"""
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value

    async def _init_client(self) -> httpx.AsyncClient:
        """初始化HTTP客户端"""
        if self._client is not None:
            await self._client.aclose()

        if self._config["use_proxy"]:
            proxies = {
                "http://": self._config["proxy"]["http"],
                "https://": self._config["proxy"]["https"]
            }
            self._client = httpx.AsyncClient(proxies=proxies, cookies=self._config["cookies"])
        else:
            self._client = httpx.AsyncClient(cookies=self._config["cookies"])

        custom_print(f"初始化HTTP客户端，当前cookies: {self._config['cookies']}")
        return self._client

    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        # 每次都重新初始化客户端，确保使用最新的cookies
        await self._init_client()
        return self._client

    async def _get_token(self) -> str:
        """
        获取登录令牌

        Returns:
            str: 登录令牌
        """
        custom_print("获取云搜网登录令牌...")

        # 默认令牌，如果所有方法都失败，将使用此令牌
        default_tokens = [
            "124afcecd3328cfc0ea0bf58e7babcbf",
            "1434fa6ea4b773abac176838ed6a97da",
            "d5a7c8bf0d65a2e86858c8d3bfdeec36"
        ]

        # 最大重试次数
        max_retries = 3

        for retry in range(max_retries):
            try:
                client = await self._get_client()

                # 构建请求头
                headers = {**self._config["common_headers"], **self._config["login_page_headers"]}

                # 访问登录页面获取令牌
                login_url = self._config["login_url"]
                custom_print(f"正在访问登录页面: {login_url} (尝试 {retry+1}/{max_retries})")

                response = await client.get(
                    login_url,
                    headers=headers,
                    timeout=60.0,
                    follow_redirects=True
                )

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"获取登录令牌失败: HTTP状态码 {response.status_code}", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        custom_print(f"已达到最大重试次数，使用默认令牌")
                        return default_tokens[0]

                # 保存cookies
                self._config["cookies"].update(dict(response.cookies))

                # 解析响应，提取令牌
                try:
                    html_content = response.text

                    # 保存响应内容的前200个字符，用于调试
                    preview = html_content[:200].replace('\n', ' ').replace('\r', ' ')
                    custom_print(f"登录页面响应预览: {preview}")

                    # 查找令牌的所有可能模式
                    token_patterns = [
                        # 方法1: 表单输入字段
                        r'name="__token__"\s+value="([^"]+)"',
                        # 方法2: JSON格式
                        r'"token"\s*:\s*"([^"]+)"',
                        # 方法3: JavaScript变量
                        r'var\s+token\s*=\s*[\'"]([^\'"]+)[\'"]',
                        # 方法4: 隐藏输入字段
                        r'<input[^>]*name=[\'"]?__token__[\'"]?[^>]*value=[\'"]?([^\'"]+)[\'"]?',
                        # 方法5: data-token属性
                        r'data-token=[\'"]([^\'"]+)[\'"]',
                        # 方法6: token变量赋值
                        r'token\s*=\s*[\'"]([^\'"]+)[\'"]'
                    ]

                    # 尝试所有模式
                    for i, pattern in enumerate(token_patterns):
                        token_match = re.search(pattern, html_content)
                        if token_match:
                            token = token_match.group(1)
                            custom_print(f"成功获取登录令牌(模式{i+1}): {token}")
                            return token

                    # 如果所有模式都失败，尝试查找任何看起来像令牌的字符串
                    # 令牌通常是32个字符的十六进制字符串
                    hex_token_match = re.search(r'[0-9a-f]{32}', html_content)
                    if hex_token_match:
                        token = hex_token_match.group(0)
                        custom_print(f"找到可能的令牌(十六进制): {token}")
                        return token

                    custom_print("未找到登录令牌", error_msg=True)

                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        custom_print(f"已达到最大重试次数，使用默认令牌")
                        return default_tokens[0]
                except Exception as e:
                    custom_print(f"解析登录页面失败: {e}", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        custom_print(f"已达到最大重试次数，使用默认令牌")
                        return default_tokens[0]
            except Exception as e:
                custom_print(f"获取登录令牌请求失败: {e}", error_msg=True)
                if retry < max_retries - 1:
                    custom_print(f"将在2秒后重试...")
                    await asyncio.sleep(2)
                    continue
                else:
                    custom_print(f"已达到最大重试次数，使用默认令牌")
                    return default_tokens[0]

        # 如果所有尝试都失败，返回默认令牌
        custom_print(f"所有获取令牌的尝试都失败，使用默认令牌")
        return default_tokens[0]

    async def _check_login_status(self, force_check: bool = False) -> bool:
        """
        检查登录状态

        Args:
            force_check: 是否强制检查，忽略登录时间

        Returns:
            bool: 是否已登录
        """
        # 检查登录时间是否过期
        current_time = time.time()
        if not force_check and self._logged_in and (current_time - self._login_time) < self._login_valid_duration:
            custom_print("登录状态未过期，无需重新检查")
            return True

        custom_print("检查云搜网登录状态...")

        try:
            client = await self._get_client()

            # 构建请求头
            headers = {**self._config["common_headers"], **self._config["login_page_headers"]}

            # 访问用户中心页面
            check_url = self._config["check_login_url"]
            custom_print(f"正在访问用户中心页面: {check_url}")

            response = await client.get(
                check_url,
                headers=headers,
                timeout=60.0,
                follow_redirects=True
            )

            # 保存cookies
            self._config["cookies"].update(dict(response.cookies))

            # 检查响应状态码
            if response.status_code != 200:
                custom_print(f"检查登录状态失败: HTTP状态码 {response.status_code}", error_msg=True)
                self._logged_in = False
                return False

            # 保存响应内容的前200个字符，用于调试
            preview = response.text[:200].replace('\n', ' ').replace('\r', ' ')
            custom_print(f"用户中心页面响应预览: {preview}")

            # 检查是否包含登录成功的标志
            login_success_indicators = [
                "退出登录", "退出", "logout", "个人中心", "用户中心", "我的账户",
                "我的资料", "修改密码", "账号设置", "albert"
            ]

            # 检查是否包含未登录的标志
            login_failure_indicators = [
                "登录", "注册", "login", "register", "未登录", "请登录"
            ]

            # 检查是否登录成功
            is_logged_in = any(indicator in response.text for indicator in login_success_indicators)
            is_not_logged_in = any(indicator in response.text for indicator in login_failure_indicators)

            # 保存检查结果
            if is_logged_in and not is_not_logged_in:
                custom_print("云搜网已登录")
                self._logged_in = True
                self._login_time = current_time
                return True
            else:
                custom_print("云搜网未登录", error_msg=True)
                self._logged_in = False

                # 清除cookies，确保下次重新登录
                self._config["cookies"] = {}

                return False
        except Exception as e:
            custom_print(f"检查登录状态失败: {e}", error_msg=True)
            self._logged_in = False
            # 清除cookies，确保下次重新登录
            self._config["cookies"] = {}
            return False

    async def _direct_login(self) -> bool:
        """
        直接使用固定的登录数据登录，不获取令牌

        Returns:
            bool: 是否登录成功
        """
        custom_print("尝试直接登录云搜网...")

        # 清除登录状态和cookies
        self._logged_in = False
        self._config["cookies"] = {}

        # 尝试多种登录方式
        login_methods = [
            self._direct_login_method1,
            self._direct_login_method2,
            self._direct_login_method3
        ]

        for i, login_method in enumerate(login_methods):
            try:
                custom_print(f"尝试登录方式 {i+1}/{len(login_methods)}...")
                success = await login_method()
                if success:
                    custom_print(f"登录方式 {i+1} 成功")
                    return True
                else:
                    custom_print(f"登录方式 {i+1} 失败")
            except Exception as e:
                custom_print(f"登录方式 {i+1} 出错: {e}", error_msg=True)

        custom_print("所有登录方式都失败")
        return False

    async def _direct_login_method1(self) -> bool:
        """登录方式1: 使用固定令牌"""
        try:
            client = await self._get_client()

            # 构建请求头
            headers = {**self._config["common_headers"], **self._config["login_headers"]}
            headers["Referer"] = self._config["login_url"]

            # 使用固定的登录数据
            login_data = {
                "url": "https://dash.yunso.net/index/user/index.html",
                "__token__": "124afcecd3328cfc0ea0bf58e7babcbf",
                "account": "albert",
                "password": "xWumeC47q9LX2LF",
                "keeplogin": "1"
            }

            # 发送登录请求
            login_url = self._config["login_url"]
            custom_print(f"正在发送直接登录请求(方式1): {login_url}")

            response = await client.post(
                login_url,
                data=login_data,
                headers=headers,
                timeout=60.0,
                follow_redirects=True
            )

            # 保存cookies
            self._config["cookies"].update(dict(response.cookies))
            custom_print(f"直接登录后的cookies: {dict(response.cookies)}")

            # 检查响应
            if response.status_code == 200:
                try:
                    response_text = response.text
                    json_data = json.loads(response_text)
                    # 检查是否登录成功或已经登录
                    if json_data.get("code") == 1:
                        # 设置登录状态
                        self._logged_in = True
                        self._login_time = time.time()

                        # 检查是否是"已经登录"的消息
                        msg = json_data.get("msg", "")
                        if "已经登录" in msg or "重复登录" in msg:
                            custom_print("检测到已经登录的消息")

                        return True
                except:
                    pass

            return False
        except Exception as e:
            custom_print(f"登录方式1失败: {e}", error_msg=True)
            return False

    async def _direct_login_method2(self) -> bool:
        """登录方式2: 先获取令牌再登录"""
        try:
            client = await self._get_client()

            # 先访问登录页面获取令牌
            login_url = self._config["login_url"]
            headers = {**self._config["common_headers"], **self._config["login_page_headers"]}

            response = await client.get(
                login_url,
                headers=headers,
                timeout=60.0,
                follow_redirects=True
            )

            # 保存cookies
            self._config["cookies"].update(dict(response.cookies))

            # 尝试提取令牌
            token = None
            response_text = response.text

            # 尝试多种提取方式
            token_patterns = [
                r'name="__token__"\s+value="([^"]+)"',  # 表单输入字段
                r'"token"\s*:\s*"([^"]+)"',  # JSON格式
                r'var\s+token\s*=\s*[\'"]([^\'"]+)[\'"]',  # JavaScript变量
                r'<input[^>]*name=[\'"]?__token__[\'"]?[^>]*value=[\'"]?([^\'"]+)[\'"]?',  # 隐藏输入字段
                r'data-token=[\'"]([^\'"]+)[\'"]',  # data-token属性
                r'token\s*=\s*[\'"]([^\'"]+)[\'"]'  # token变量赋值
            ]

            for pattern in token_patterns:
                matches = re.findall(pattern, response_text)
                if matches:
                    token = matches[0]
                    custom_print(f"成功获取登录令牌: {token}")
                    break

            if not token:
                # 尝试查找任何看起来像令牌的十六进制字符串
                hex_pattern = r'[0-9a-f]{32}'
                matches = re.findall(hex_pattern, response_text)
                if matches:
                    token = matches[0]
                    custom_print(f"找到可能的令牌(十六进制): {token}")

            if not token:
                custom_print("无法获取登录令牌，使用默认令牌")
                token = "124afcecd3328cfc0ea0bf58e7babcbf"

            # 构建登录数据
            login_data = {
                "url": "https://dash.yunso.net/index/user/index.html",
                "__token__": token,
                "account": "albert",
                "password": "xWumeC47q9LX2LF",
                "keeplogin": "1"
            }

            # 构建请求头
            headers = {**self._config["common_headers"], **self._config["login_headers"]}
            headers["Referer"] = self._config["login_url"]

            # 发送登录请求
            custom_print(f"正在发送直接登录请求(方式2): {login_url}")

            response = await client.post(
                login_url,
                data=login_data,
                headers=headers,
                timeout=60.0,
                follow_redirects=True
            )

            # 保存cookies
            self._config["cookies"].update(dict(response.cookies))
            custom_print(f"直接登录后的cookies: {dict(response.cookies)}")

            # 检查响应
            if response.status_code == 200:
                try:
                    response_text = response.text
                    json_data = json.loads(response_text)
                    # 检查是否登录成功或已经登录
                    if json_data.get("code") == 1:
                        # 设置登录状态
                        self._logged_in = True
                        self._login_time = time.time()

                        # 检查是否是"已经登录"的消息
                        msg = json_data.get("msg", "")
                        if "已经登录" in msg or "重复登录" in msg:
                            custom_print("检测到已经登录的消息")

                        return True
                except:
                    pass

            return False
        except Exception as e:
            custom_print(f"登录方式2失败: {e}", error_msg=True)
            return False

    async def _direct_login_method3(self) -> bool:
        """登录方式3: 使用固定cookies"""
        try:
            # 直接设置固定的cookies
            self._config["cookies"] = {
                "PHPSESSID": "51e618bcfc605e0912ff563d421199bb",
                "fastadmin_uid": "10106",
                "fastadmin_token": "7e15fbc7-ec46-4cc9-ada5-a1002f74b99e"
            }

            custom_print(f"设置固定cookies: {self._config['cookies']}")

            # 验证cookies是否有效
            client = await self._get_client()

            # 访问用户中心页面
            check_url = self._config["check_login_url"]
            headers = {**self._config["common_headers"], **self._config["login_page_headers"]}

            response = await client.get(
                check_url,
                headers=headers,
                timeout=60.0,
                follow_redirects=True
            )

            # 检查响应
            if response.status_code == 200:
                response_text = response.text

                # 检查是否包含登录成功的标志
                login_success_indicators = [
                    "退出登录", "退出", "logout", "个人中心", "用户中心", "我的账户",
                    "我的资料", "修改密码", "账号设置", "albert"
                ]

                if any(indicator in response_text for indicator in login_success_indicators):
                    # 设置登录状态
                    self._logged_in = True
                    self._login_time = time.time()
                    return True

            return False
        except Exception as e:
            custom_print(f"登录方式3失败: {e}", error_msg=True)
            return False

    async def login(self) -> bool:
        """
        登录云搜网

        Returns:
            bool: 是否登录成功
        """
        # 先检查是否已登录，强制检查登录状态
        if await self._check_login_status(force_check=True):
            custom_print("已经登录云搜网，无需重复登录")
            return True

        custom_print("开始登录云搜网...")

        # 清除登录状态和cookies
        self._logged_in = False
        self._config["cookies"] = {}

        # 先尝试直接登录
        direct_login_success = await self._direct_login()
        if direct_login_success:
            return True

        # 如果直接登录失败，尝试常规登录流程
        custom_print("直接登录失败，尝试常规登录流程")

        # 检查是否已经登录(可能在直接登录过程中已经登录成功)
        if self._logged_in:
            custom_print("检测到已经登录成功，跳过常规登录流程")
            return True

        # 最大重试次数
        max_retries = 3

        for retry in range(max_retries):
            try:
                # 先获取最新的令牌
                token = await self._get_token()
                if not token:
                    custom_print("获取登录令牌失败，无法登录", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试登录 (尝试 {retry+2}/{max_retries})...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        return False

                client = await self._get_client()

                # 构建请求头
                headers = {**self._config["common_headers"], **self._config["login_headers"]}
                headers["Referer"] = self._config["login_url"]

                # 构建登录表单数据
                login_data = dict(self._config["login_credentials"])
                login_data["__token__"] = token  # 使用最新的令牌

                # 发送登录请求
                login_url = self._config["login_url"]
                custom_print(f"正在发送登录请求: {login_url} (尝试 {retry+1}/{max_retries})")
                custom_print(f"登录数据: {login_data}")

                response = await client.post(
                    login_url,
                    data=login_data,  # 使用data而不是content，让httpx自动处理表单编码
                    headers=headers,
                    timeout=60.0,
                    follow_redirects=True
                )

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"登录云搜网失败: HTTP状态码 {response.status_code}", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试登录...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        return False

                # 保存cookies
                self._config["cookies"].update(dict(response.cookies))
                custom_print(f"登录后的cookies: {dict(response.cookies)}")

                # 解析响应
                try:
                    response_text = response.text
                    custom_print(f"登录响应: {response_text}")

                    # 尝试解析JSON
                    try:
                        json_data = json.loads(response_text)

                        # 检查是否登录成功或已经登录
                        if json_data.get("code") == 1:
                            custom_print("登录云搜网成功")
                            # 设置登录状态
                            self._logged_in = True
                            self._login_time = time.time()

                            # 检查是否是"已经登录"的消息
                            msg = json_data.get("msg", "")
                            if "已经登录" in msg or "重复登录" in msg:
                                custom_print("检测到已经登录的消息")

                            # 直接返回成功，不再验证登录状态
                            custom_print("登录成功，设置登录状态")
                            return True
                        else:
                            error_msg = json_data.get("msg", "未知错误")
                            custom_print(f"登录云搜网失败: {error_msg}", error_msg=True)

                            # 如果是令牌无效，尝试使用响应中的新令牌
                            if "令牌" in error_msg and json_data.get("data", {}).get("token"):
                                new_token = json_data["data"]["token"]
                                custom_print(f"获取到新令牌: {new_token}，尝试重新登录")

                                # 更新令牌并重新登录
                                login_data["__token__"] = new_token

                                # 发送登录请求
                                custom_print(f"使用新令牌重新发送登录请求")
                                response = await client.post(
                                    login_url,
                                    data=login_data,
                                    headers=headers,
                                    timeout=60.0,
                                    follow_redirects=True
                                )

                                # 保存cookies
                                self._config["cookies"].update(dict(response.cookies))

                                # 解析响应
                                response_text = response.text
                                custom_print(f"重新登录响应: {response_text}")

                                try:
                                    json_data = json.loads(response_text)
                                    # 检查是否登录成功或已经登录
                                    if json_data.get("code") == 1:
                                        custom_print("重新登录云搜网成功")
                                        # 设置登录状态
                                        self._logged_in = True
                                        self._login_time = time.time()

                                        # 检查是否是"已经登录"的消息
                                        msg = json_data.get("msg", "")
                                        if "已经登录" in msg or "重复登录" in msg:
                                            custom_print("检测到已经登录的消息")

                                        # 直接返回成功，不再验证登录状态
                                        custom_print("重新登录成功，设置登录状态")
                                        return True
                                    else:
                                        error_msg = json_data.get("msg", "未知错误")
                                        custom_print(f"重新登录云搜网失败: {error_msg}", error_msg=True)
                                        if retry < max_retries - 1:
                                            custom_print(f"将在2秒后重试登录...")
                                            await asyncio.sleep(2)
                                            continue
                                        else:
                                            return False
                                except Exception as e:
                                    custom_print(f"解析重新登录响应失败: {e}", error_msg=True)
                                    if retry < max_retries - 1:
                                        custom_print(f"将在2秒后重试登录...")
                                        await asyncio.sleep(2)
                                        continue
                                    else:
                                        return False

                            # 如果不是令牌问题，检查是否需要重试
                            if retry < max_retries - 1:
                                custom_print(f"将在2秒后重试登录...")
                                await asyncio.sleep(2)
                                continue
                            else:
                                return False
                    except json.JSONDecodeError:
                        # 响应不是JSON格式，检查是否包含成功信息
                        if "登录成功" in response_text or "成功登录" in response_text:
                            custom_print("登录云搜网成功(非JSON响应)")
                            # 设置登录状态
                            self._logged_in = True
                            self._login_time = time.time()

                            # 直接返回成功，不再验证登录状态
                            custom_print("登录成功(非JSON响应)，设置登录状态")
                            return True
                        else:
                            custom_print(f"登录响应不是JSON格式，且未找到成功信息", error_msg=True)
                            if retry < max_retries - 1:
                                custom_print(f"将在2秒后重试登录...")
                                await asyncio.sleep(2)
                                continue
                            else:
                                # 最后一次尝试，检查是否有cookies，如果有则认为登录成功
                                if response.cookies:
                                    custom_print("虽然响应异常，但获取到了cookies，尝试继续操作")
                                    # 设置登录状态
                                    self._logged_in = True
                                    self._login_time = time.time()

                                    # 直接返回成功，不再验证登录状态
                                    custom_print("登录成功(通过cookies)，设置登录状态")
                                    return True
                                return False
                except Exception as e:
                    custom_print(f"解析登录响应失败: {e}", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试登录...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        # 最后一次尝试，检查是否有cookies，如果有则认为登录成功
                        if response.cookies:
                            custom_print("虽然响应异常，但获取到了cookies，尝试继续操作")
                            # 设置登录状态
                            self._logged_in = True
                            self._login_time = time.time()

                            # 直接返回成功，不再验证登录状态
                            custom_print("登录成功(通过异常处理cookies)，设置登录状态")
                            return True
                        return False
            except Exception as e:
                custom_print(f"登录云搜网请求失败: {e}", error_msg=True)
                if retry < max_retries - 1:
                    custom_print(f"将在2秒后重试登录...")
                    await asyncio.sleep(2)
                    continue
                else:
                    return False

        return False

    async def _visit_dataentry_page(self) -> bool:
        """
        访问资源提交页面，确保登录状态有效

        Returns:
            bool: 是否成功访问
        """
        # 最大重试次数
        max_retries = 3

        for retry in range(max_retries):
            try:
                client = await self._get_client()

                # 构建请求头
                headers = {**self._config["common_headers"], **self._config["login_page_headers"]}

                # 访问资源提交页面
                dataentry_url = self._config["dataentry_url"]
                custom_print(f"正在访问资源提交页面: {dataentry_url} (尝试 {retry+1}/{max_retries})")

                response = await client.get(
                    dataentry_url,
                    headers=headers,
                    timeout=60.0,
                    follow_redirects=True
                )

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"访问资源提交页面失败: HTTP状态码 {response.status_code}", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        return False

                # 保存cookies
                self._config["cookies"].update(dict(response.cookies))
                custom_print(f"资源提交页面cookies: {dict(response.cookies)}")

                # 保存响应内容的前200个字符，用于调试
                preview = response.text[:200].replace('\n', ' ').replace('\r', ' ')
                custom_print(f"资源提交页面响应预览: {preview}")

                # 检查是否需要登录
                login_indicators = ["需要登录", "请先登录", "登录后", "未登录", "请登录"]
                needs_login = any(indicator in response.text for indicator in login_indicators)

                if needs_login:
                    custom_print("访问资源提交页面时发现需要登录", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        return False

                # 检查是否包含提交表单或提交按钮，确认页面是正确的
                form_indicators = ["提交", "上传", "分享", "资源", "dataentry", "form"]
                has_form = any(indicator in response.text.lower() for indicator in form_indicators)

                if not has_form:
                    custom_print("资源提交页面可能不正确，未找到提交表单", error_msg=True)
                    if retry < max_retries - 1:
                        custom_print(f"将在2秒后重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        # 最后一次尝试，如果有cookies，尝试继续操作
                        if response.cookies:
                            custom_print("虽然页面可能不正确，但获取到了cookies，尝试继续操作")
                            return True
                        return False

                custom_print("成功访问资源提交页面")
                return True
            except Exception as e:
                custom_print(f"访问资源提交页面失败: {e}", error_msg=True)
                if retry < max_retries - 1:
                    custom_print(f"将在2秒后重试...")
                    await asyncio.sleep(2)
                    continue
                else:
                    return False

        return False

    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "提交器已禁用"}

        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 检查登录状态，强制检查
        login_status = await self._check_login_status(force_check=True)
        if not login_status:
            custom_print("未登录或登录已过期，尝试登录")
            login_success = await self.login()
            if not login_success:
                return {"code": -1, "message": "登录云搜网失败，无法提交资源"}

        # 访问资源提交页面，确保登录状态有效
        visit_success = await self._visit_dataentry_page()
        if not visit_success:
            # 尝试重新登录
            login_success = await self.login()
            if not login_success:
                return {"code": -1, "message": "登录云搜网失败，无法提交资源"}

            # 再次访问资源提交页面
            visit_success = await self._visit_dataentry_page()
            if not visit_success:
                return {"code": -1, "message": "访问资源提交页面失败，无法提交资源"}

        # 分批提交
        batch_size = self._config["batch_size"]
        results = []

        for i in range(0, len(share_links), batch_size):
            batch_links = share_links[i:i+batch_size]

            # 将链接列表转换为字符串并进行base64编码
            links_str = "\n".join(batch_links)
            encoded_links = base64.b64encode(links_str.encode('utf-8')).decode('utf-8')

            # 构建请求数据
            data = {
                "data": encoded_links,
                "Publicity": self._config["publicity"]
            }

            # 构建请求URL
            api_url = self._config["api_url"]

            try:
                client = await self._get_client()

                # 构建请求头
                headers = {**self._config["common_headers"], **self._config["submit_headers"]}
                headers["Referer"] = self._config["dataentry_url"]

                # 发送请求
                response = await client.post(
                    api_url,
                    data=data,
                    headers=headers,
                    timeout=60.0
                )

                # 检查响应状态码
                if response.status_code != 200:
                    custom_print(f"提交资源失败: HTTP状态码 {response.status_code}", error_msg=True)
                    results.append({
                        "code": response.status_code,
                        "message": f"HTTP错误: {response.status_code}",
                        "links": batch_links
                    })
                    continue

                # 解析响应
                response_text = response.text
                custom_print(f"收到响应(HTTP 200)，响应内容: {response_text}")

                # 检查响应中是否包含成功关键词
                success_indicators = ["操作成功", "提交成功", "success", "成功"]
                success = any(indicator in response_text for indicator in success_indicators)

                if success:
                    custom_print(f"成功提交 {len(batch_links)} 个资源链接到云搜网")
                    results.append({
                        "code": 0,
                        "message": "提交成功",
                        "links": batch_links,
                        "response": {"status": "success", "message": "操作成功"}
                    })
                else:
                    # 尝试解析为JSON，看是否有更详细的错误信息
                    try:
                        json_data = json.loads(response_text)

                        # 检查JSON响应是否表示成功
                        if json_data.get("code") == 0 or json_data.get("code") == 1 or json_data.get("status") == 1:
                            custom_print(f"成功提交 {len(batch_links)} 个资源链接到云搜网 (JSON响应)")
                            results.append({
                                "code": 0,
                                "message": "提交成功",
                                "links": batch_links,
                                "response": json_data
                            })
                            continue

                        # 提取错误信息
                        error_msg = json_data.get("message", json_data.get("msg", "未知错误"))

                        # 检查是否是重复提交
                        duplicate_indicators = ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]
                        is_duplicate = any(indicator in error_msg for indicator in duplicate_indicators)

                        if is_duplicate:
                            # 提取具体的重复链接信息(如果有)
                            duplicate_links = []
                            try:
                                if isinstance(json_data.get("data"), list):
                                    duplicate_links = json_data.get("data")
                                elif isinstance(json_data.get("data"), str) and json_data.get("data"):
                                    duplicate_links = [json_data.get("data")]
                            except:
                                pass

                            if duplicate_links:
                                custom_print(f"检测到重复提交: {error_msg}, 重复链接: {duplicate_links}")
                            else:
                                custom_print(f"检测到重复提交: {error_msg}")

                            # 将重复提交视为成功
                            results.append({
                                "code": 0,
                                "message": "资源已存在(视为成功)",
                                "links": batch_links,
                                "duplicate_links": duplicate_links if duplicate_links else None,
                                "response": json_data
                            })
                            continue

                        # 检查是否需要登录
                        login_indicators = ["需要登录", "请先登录", "登录后", "未登录", "请登录"]
                        needs_login = any(indicator in error_msg for indicator in login_indicators)

                        if needs_login or json_data.get("code") == -1 and "需要登" in error_msg:
                            custom_print("提交资源时发现需要登录，尝试重新登录", error_msg=True)

                            # 清除登录状态和cookies
                            self._logged_in = False
                            self._config["cookies"] = {}

                            # 尝试重新登录
                            login_success = await self.login()
                            if login_success:
                                # 再次访问资源提交页面
                                visit_success = await self._visit_dataentry_page()
                                if visit_success:
                                    # 重新发送请求
                                    custom_print(f"重新登录成功，尝试重新提交资源")
                                    response = await client.post(
                                        api_url,
                                        data=data,
                                        headers=headers,
                                        timeout=60.0
                                    )

                                    # 解析响应
                                    response_text = response.text
                                    custom_print(f"重新提交响应: {response_text}")

                                    # 检查重新提交是否成功
                                    success = any(indicator in response_text for indicator in success_indicators)

                                    if success:
                                        custom_print(f"重新提交成功 {len(batch_links)} 个资源链接到云搜网")
                                        results.append({
                                            "code": 0,
                                            "message": "提交成功",
                                            "links": batch_links,
                                            "response": {"status": "success", "message": "操作成功"}
                                        })
                                        continue
                                    else:
                                        try:
                                            # 尝试解析JSON响应
                                            json_data = json.loads(response_text)
                                            if json_data.get("code") == 0 or json_data.get("code") == 1 or json_data.get("status") == 1:
                                                custom_print(f"重新提交成功 {len(batch_links)} 个资源链接到云搜网 (JSON响应)")
                                                results.append({
                                                    "code": 0,
                                                    "message": "提交成功",
                                                    "links": batch_links,
                                                    "response": json_data
                                                })
                                                continue

                                            # 检查是否是重复提交
                                            error_msg = json_data.get("message", json_data.get("msg", "未知错误"))
                                            duplicate_indicators = ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]
                                            is_duplicate = any(indicator in error_msg for indicator in duplicate_indicators)

                                            if is_duplicate:
                                                # 提取具体的重复链接信息(如果有)
                                                duplicate_links = []
                                                try:
                                                    if isinstance(json_data.get("data"), list):
                                                        duplicate_links = json_data.get("data")
                                                    elif isinstance(json_data.get("data"), str) and json_data.get("data"):
                                                        duplicate_links = [json_data.get("data")]
                                                except:
                                                    pass

                                                if duplicate_links:
                                                    custom_print(f"检测到重复提交: {error_msg}, 重复链接: {duplicate_links}")
                                                else:
                                                    custom_print(f"检测到重复提交: {error_msg}")

                                                # 将重复提交视为成功
                                                results.append({
                                                    "code": 0,
                                                    "message": "资源已存在(视为成功)",
                                                    "links": batch_links,
                                                    "duplicate_links": duplicate_links if duplicate_links else None,
                                                    "response": json_data
                                                })
                                                continue
                                        except:
                                            pass

                        custom_print(f"提交资源失败: {error_msg}", error_msg=True)
                        results.append({
                            "code": -1,
                            "message": error_msg,
                            "links": batch_links,
                            "response": json_data
                        })
                    except json.JSONDecodeError:
                        # 不是JSON格式，但也没有成功关键词
                        # 检查是否包含其他可能表示成功的内容
                        if "成功" in response_text or "已提交" in response_text or "已接收" in response_text:
                            custom_print(f"成功提交 {len(batch_links)} 个资源链接到云搜网 (非标准响应)")
                            results.append({
                                "code": 0,
                                "message": "提交成功",
                                "links": batch_links,
                                "response": {"status": "success", "message": "操作成功 (非标准响应)"}
                            })
                        # 检查是否是重复提交
                        elif any(indicator in response_text for indicator in ["已存在", "重复", "已提交", "已添加", "已经存在", "duplicate"]):
                            # 尝试提取重复链接信息
                            duplicate_links = []
                            try:
                                # 尝试使用正则表达式提取链接
                                import re
                                links = re.findall(r'https?://[^\s<>"\']+|www\.[^\s<>"\']+', response_text)
                                if links:
                                    duplicate_links = links
                            except:
                                pass

                            if duplicate_links:
                                custom_print(f"检测到重复提交 (非JSON响应), 可能的重复链接: {duplicate_links}")
                            else:
                                custom_print(f"检测到重复提交 (非JSON响应)")

                            # 将重复提交视为成功
                            results.append({
                                "code": 0,
                                "message": "资源已存在(视为成功)",
                                "links": batch_links,
                                "duplicate_links": duplicate_links if duplicate_links else None,
                                "response": {"status": "success", "message": "资源已存在 (视为成功)"}
                            })
                        else:
                            custom_print(f"提交资源失败: 响应中没有成功关键词", error_msg=True)
                            results.append({
                                "code": -1,
                                "message": "响应中没有成功关键词",
                                "links": batch_links,
                                "response": {"raw_response": response_text}
                            })
            except Exception as e:
                custom_print(f"提交资源请求失败: {e}", error_msg=True)
                results.append({
                    "code": -1,
                    "message": f"请求失败: {e}",
                    "links": batch_links
                })

            # 随机延迟1-3秒，避免请求过快
            if i + batch_size < len(share_links):
                delay = random.uniform(1, 3)
                custom_print(f"等待 {delay:.2f} 秒后提交下一批...")
                await asyncio.sleep(delay)

        # 汇总结果
        success_count = sum(1 for r in results if r["code"] == 0)
        duplicate_count = sum(1 for r in results if r["code"] == 0 and "已存在" in r["message"])
        new_success_count = success_count - duplicate_count
        total_count = len(results)

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"成功提交 {success_count}/{total_count} 批资源到云搜网 (新增: {new_success_count}, 已存在: {duplicate_count})",
            "details": results
        }

    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态 (云搜网API不支持此功能)

        Args:
            submission_id: 提交ID

        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        # 忽略submission_id参数，因为云搜网API不支持检查提交状态
        _ = submission_id

        return {
            "code": -1,
            "message": "云搜网API不支持检查提交状态"
        }

    async def close(self) -> None:
        """关闭HTTP客户端"""
        if self._client is not None:
            await self._client.aclose()
            self._client = None