"""
资源提交模块，用于将分享的资源提交到各个资源站点
"""
from .base_submitter import BaseSubmitter
from .krzb_submitter import KrzbSubmitter
from .yunso_submitter import YunsoSubmitter
from .yiso_submitter import YisoSubmitter
from .buyutu_submitter import BuyutuSubmitter
from .sroad_submitter import SroadSubmitter
from .macyeah_submitter import MacyeahSubmitter

# 注册所有的提交器
SUBMITTERS = {
    "krzb": KrzbSubmitter,
    "yunso": YunsoSubmitter,
    "yiso": YisoSubmitter,
    "buyutu": BuyutuSubmitter,
    "sroad": SroadSubmitter,
    "macyeah": MacyeahSubmitter,
}

def get_submitter(submitter_type: str) -> BaseSubmitter:
    """
    根据提交器类型获取对应的提交器实例

    Args:
        submitter_type: 提交器类型

    Returns:
        BaseSubmitter: 提交器实例
    """
    if submitter_type not in SUBMITTERS:
        raise ValueError(f"不支持的提交器类型: {submitter_type}")

    return SUBMITTERS[submitter_type]()
