"""
KRZB资源提交器
"""
import json
import random
import asyncio
import time
from typing import List, Dict, Any, Union

import httpx

from utils import custom_print, get_timestamp
from .base_submitter import BaseSubmitter


class KrzbSubmitter(BaseSubmitter):
    """KRZB资源提交器"""

    def __init__(self):
        """初始化KRZB资源提交器"""
        self._config = {
            "enabled": True,
            "api_url": "https://fc-resource-node-api.krzb.net",
            "batch_size": 4,  # 每次提交的链接数量
            "use_proxy": False,
            "proxy": {
                "http": "http://127.0.0.1:7890",
                "https": "http://127.0.0.1:7890"
            },
            "headers": {
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Content-Type": "application/json"
            }
        }

    @property
    def name(self) -> str:
        """提交器名称"""
        return "KRZB资源提交器"

    def get_config(self) -> Dict[str, Any]:
        """获取提交器配置"""
        return self._config

    def set_config(self, config: Dict[str, Any]) -> None:
        """设置提交器配置"""
        if not isinstance(config, dict):
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                if isinstance(value, dict) and isinstance(self._config[key], dict):
                    # 递归更新嵌套字典
                    self._config[key].update(value)
                else:
                    self._config[key] = value

    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self._config["use_proxy"]:
            proxies = {
                "http://": self._config["proxy"]["http"],
                "https://": self._config["proxy"]["https"]
            }
            return httpx.AsyncClient(proxies=proxies)
        else:
            return httpx.AsyncClient()

    async def submit(self, share_links: List[str]) -> Dict[str, Any]:
        """
        提交资源分享链接

        Args:
            share_links: 分享链接列表

        Returns:
            Dict: 提交结果，包含状态码和消息
        """
        if not self._config["enabled"]:
            return {"code": -1, "message": "提交器已禁用"}

        if not share_links:
            return {"code": -1, "message": "没有可提交的链接"}

        # 获取当前时间戳
        timestamp = get_timestamp(13)

        # 构建API URL
        api_url = f"{self._config['api_url']}/api/v1/pan/submitResource?t={timestamp}&version=v2"

        # 分批提交
        batch_size = self._config["batch_size"]
        results = []

        for i in range(0, len(share_links), batch_size):
            batch_links = share_links[i:i+batch_size]

            # 构建请求数据
            data = {
                "data": [
                    {"url": link, "pwd": "", "msg": "等待上传"} for link in batch_links
                ]
            }

            try:
                async with await self._get_client() as client:
                    timeout = httpx.Timeout(60.0, connect=60.0)
                    response = await client.post(
                        api_url,
                        json=data,
                        headers=self._config["headers"],
                        timeout=timeout
                    )

                    # 检查响应状态码
                    if response.status_code != 200:
                        custom_print(f"提交资源失败: HTTP状态码 {response.status_code}", error_msg=True)
                        results.append({
                            "code": response.status_code,
                            "message": f"HTTP错误: {response.status_code}",
                            "links": batch_links
                        })
                        continue

                    # 解析响应
                    try:
                        json_data = response.json()
                        custom_print(f"提交资源响应: {json.dumps(json_data, ensure_ascii=False)}")

                        if json_data.get("code") == 0:
                            custom_print(f"成功提交 {len(batch_links)} 个资源链接")
                            results.append({
                                "code": 0,
                                "message": "提交成功",
                                "links": batch_links,
                                "response": json_data
                            })
                        else:
                            custom_print(f"提交资源失败: {json_data.get('message', '未知错误')}", error_msg=True)
                            results.append({
                                "code": json_data.get("code", -1),
                                "message": json_data.get("message", "未知错误"),
                                "links": batch_links,
                                "response": json_data
                            })
                    except Exception as e:
                        custom_print(f"解析提交响应失败: {e}", error_msg=True)
                        results.append({
                            "code": -1,
                            "message": f"解析响应失败: {e}",
                            "links": batch_links
                        })
            except Exception as e:
                custom_print(f"提交资源请求失败: {e}", error_msg=True)
                results.append({
                    "code": -1,
                    "message": f"请求失败: {e}",
                    "links": batch_links
                })

            # 随机延迟1-3秒，避免请求过快
            if i + batch_size < len(share_links):
                delay = random.uniform(1, 3)
                custom_print(f"等待 {delay:.2f} 秒后提交下一批...")
                await asyncio.sleep(delay)

        # 汇总结果
        success_count = sum(1 for r in results if r["code"] == 0)
        total_count = len(results)

        return {
            "code": 0 if success_count > 0 else -1,
            "message": f"成功提交 {success_count}/{total_count} 批资源",
            "details": results
        }

    async def check_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        检查提交状态 (KRZB API不支持此功能)

        Args:
            submission_id: 提交ID

        Returns:
            Dict: 提交状态，包含状态码和消息
        """
        return {
            "code": -1,
            "message": "KRZB API不支持检查提交状态"
        }
