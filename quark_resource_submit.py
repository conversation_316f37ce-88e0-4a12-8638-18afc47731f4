"""
夸克网盘资源提交功能
"""
import asyncio
import os
import sqlite3
from typing import List, Dict, Any, Optional

from utils import custom_print, get_datetime


async def submit_share_links(resource_submit_manager, share_links: List[str]) -> Dict[str, Any]:
    """
    提交分享链接到资源站点

    Args:
        resource_submit_manager: 资源提交管理器
        share_links: 分享链接列表

    Returns:
        Dict: 提交结果
    """
    if not resource_submit_manager:
        return {"code": -1, "message": "资源提交管理器未初始化"}

    if not share_links:
        return {"code": -1, "message": "没有可提交的链接"}

    # 去重
    share_links = list(set(share_links))

    custom_print(f"开始提交 {len(share_links)} 个分享链接到资源站点")

    try:
        # 提交链接
        result = await resource_submit_manager.submit_links(share_links)
        return result
    finally:
        # 关闭资源提交管理器
        await resource_submit_manager.close()


async def submit_unsubmitted_links(resource_submit_manager, limit: int = 50) -> Dict[str, Any]:
    """
    从数据库中查询未提交的分享记录并提交

    Args:
        resource_submit_manager: 资源提交管理器
        limit: 最大查询记录数

    Returns:
        Dict: 提交结果
    """
    if not resource_submit_manager:
        return {"code": -1, "message": "资源提交管理器未初始化"}

    try:
        # 提交未提交的链接
        result = await resource_submit_manager.submit_from_database(limit)

        # 显示每个站点的未提交链接数量
        if "unsubmitted_counts" in result:
            unsubmitted_counts = result["unsubmitted_counts"]
            custom_print("=" * 50)
            custom_print("各站点未提交链接数量详情:")
            for submitter_type, count in unsubmitted_counts.items():
                # 获取提交器名称
                submitter = resource_submit_manager.get_submitter(submitter_type)
                submitter_name = submitter.name if submitter else submitter_type

                # 显示站点名称和未提交链接数量
                if submitter_type == "sroad":
                    custom_print(f"- Sroad站点: {count} 条未提交链接")
                elif submitter_type == "buyutu":
                    custom_print(f"- 步游兔站点: {count} 条未提交链接")
                elif submitter_type == "yiso":
                    custom_print(f"- 一搜站点: {count} 条未提交链接")
                elif submitter_type == "krzb":
                    custom_print(f"- 快融站点: {count} 条未提交链接")
                elif submitter_type == "macyeah":
                    custom_print(f"- 马克耶网盘站点: {count} 条未提交链接")
                else:
                    custom_print(f"- {submitter_name}: {count} 条未提交链接")
            custom_print("=" * 50)

        return result
    finally:
        # 关闭资源提交管理器
        await resource_submit_manager.close()


def ensure_submit_fields_exist() -> bool:
    """
    确保share_records表中存在提交相关的字段

    Returns:
        bool: 是否成功
    """
    try:
        # 确保数据库目录存在
        os.makedirs('data', exist_ok=True)

        # 连接数据库
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 检查share_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        table_exists = cursor.fetchone()

        if not table_exists:
            custom_print("分享记录表不存在，无法添加提交字段", error_msg=True)
            conn.close()
            return False

        # 检查是否存在submitted字段
        cursor.execute("PRAGMA table_info(share_records)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        # 如果不存在submitted字段，添加该字段
        if "submitted" not in column_names:
            try:
                cursor.execute("ALTER TABLE share_records ADD COLUMN submitted INTEGER DEFAULT 0")
                cursor.execute("ALTER TABLE share_records ADD COLUMN submit_time TEXT")
                cursor.execute("ALTER TABLE share_records ADD COLUMN submit_result TEXT")
                conn.commit()
                custom_print("已添加资源提交相关字段到分享记录表")
            except sqlite3.OperationalError as e:
                custom_print(f"添加字段失败: {e}", error_msg=True)
                conn.close()
                return False

        # 确保submit_records表存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        submit_table_exists = cursor.fetchone()

        if not submit_table_exists:
            # 创建提交记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS submit_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                share_url TEXT NOT NULL,
                submitter_type TEXT NOT NULL,
                submit_time TEXT NOT NULL,
                status INTEGER DEFAULT 1,
                response TEXT,
                UNIQUE(share_url, submitter_type)
            )
            ''')
            conn.commit()
            custom_print("已创建资源提交记录表")

        conn.close()
        return True
    except Exception as e:
        custom_print(f"确保提交字段存在时出错: {e}", error_msg=True)
        return False


def initialize_new_submitter(submitter_type: str) -> bool:
    """
    初始化新的提交器，确保数据库中存在相关表和字段

    Args:
        submitter_type: 提交器类型

    Returns:
        bool: 是否成功
    """
    try:
        # 显示友好的提交器名称
        if submitter_type == "sroad":
            display_name = "Sroad站点"
        elif submitter_type == "buyutu":
            display_name = "步游兔站点"
        elif submitter_type == "yiso":
            display_name = "一搜站点"
        elif submitter_type == "krzb":
            display_name = "快融站点"
        elif submitter_type == "macyeah":
            display_name = "马克耶网盘站点"
        else:
            display_name = submitter_type

        custom_print(f"正在初始化{display_name}提交器...")

        # 确保数据库目录存在
        os.makedirs('data', exist_ok=True)

        # 连接数据库
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 确保submit_records表存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        submit_table_exists = cursor.fetchone()

        if not submit_table_exists:
            # 创建提交记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS submit_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                share_url TEXT NOT NULL,
                submitter_type TEXT NOT NULL,
                submit_time TEXT NOT NULL,
                status INTEGER DEFAULT 1,
                response TEXT,
                UNIQUE(share_url, submitter_type)
            )
            ''')
            conn.commit()
            custom_print(f"已创建资源提交记录表，准备添加{display_name}提交器")

        # 检查是否有share_records表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='share_records'")
        share_table_exists = cursor.fetchone()

        if share_table_exists:
            # 获取所有分享链接
            cursor.execute("SELECT COUNT(*) FROM share_records WHERE status = 1 AND share_url != ''")
            count = cursor.fetchone()[0]
            custom_print(f"数据库中有 {count} 条分享链接记录")

            # 获取已提交的链接数量
            cursor.execute(
                """SELECT COUNT(*) FROM submit_records
                   WHERE submitter_type = ? AND status = 1""",
                (submitter_type,)
            )
            submitted_count = cursor.fetchone()[0]

            # 获取未提交的链接数量
            cursor.execute(
                """SELECT COUNT(*) FROM share_records s
                   WHERE s.status = 1 AND s.share_url != ''
                   AND NOT EXISTS (
                       SELECT 1 FROM submit_records sr
                       WHERE sr.share_url = s.share_url
                       AND sr.submitter_type = ?
                       AND sr.status = 1
                   )""",
                (submitter_type,)
            )
            unsubmitted_count = cursor.fetchone()[0]

            if unsubmitted_count == 0:
                custom_print(f"所有 {submitted_count} 条分享链接都已提交到{display_name}，无需再次提交")
            else:
                custom_print(f"其中 {submitted_count} 条已提交到{display_name}，{unsubmitted_count} 条未提交")
                custom_print(f"有 {unsubmitted_count} 条分享链接可提交到{display_name}")
        else:
            custom_print(f"分享记录表不存在，请先获取RSS源并生成分享链接，然后再提交到{display_name}", error_msg=True)

        conn.close()
        custom_print(f"{display_name}提交器初始化完成")
        return True
    except Exception as e:
        custom_print(f"初始化{submitter_type}提交器时出错: {e}", error_msg=True)
        return False
