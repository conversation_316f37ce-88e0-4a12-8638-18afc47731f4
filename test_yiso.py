#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sqlite3
from resource_submitters.resource_submit_manager import ResourceSubmitManager
from utils import custom_print

async def test_yiso_submitter():
    """测试易搜提交器"""
    # 初始化资源提交管理器
    config = {
        "enabled": True,
        "auto_submit": True,
        "submitters": ["yiso"],
        "submit_delay": {
            "min": 1,
            "max": 2
        },
        "avoid_duplicate": True,
        "yiso": {
            "enabled": True,
            "api_url": "https://yiso.fun/api/member/share",
            "batch_size": 1,
            "use_proxy": False
        }
    }

    resource_submit_manager = ResourceSubmitManager(config)

    try:
        # 从数据库中获取一条分享记录
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()

        # 获取最近的10条分享记录
        cursor.execute("SELECT share_url FROM share_records WHERE status = 1 ORDER BY id DESC LIMIT 10")
        results = cursor.fetchall()
        conn.close()

        if not results:
            custom_print("没有找到分享记录", error_msg=True)
            return

        # 使用所有获取到的链接
        share_urls = [row[0] for row in results]
        custom_print(f"找到 {len(share_urls)} 条分享记录")

        # 只使用第一条进行测试
        share_url = share_urls[0]
        custom_print(f"测试提交链接: {share_url}")

        # 提交链接
        result = await resource_submit_manager.submit_links([share_url])

        if result.get("code") == 0:
            custom_print(f"测试成功: {result.get('message')}")

            # 打印详细信息
            if "details" in result and "yiso" in result["details"]:
                yiso_result = result["details"]["yiso"]
                custom_print(f"易搜提交结果: {yiso_result.get('message')}")

                # 打印每批次的详细信息
                if "details" in yiso_result:
                    for i, batch_result in enumerate(yiso_result["details"]):
                        custom_print(f"批次 {i+1} 结果: {batch_result.get('message')}")
        else:
            custom_print(f"测试失败: {result.get('message')}", error_msg=True)
    finally:
        # 关闭资源提交管理器
        await resource_submit_manager.close()

if __name__ == "__main__":
    asyncio.run(test_yiso_submitter())
