{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_connectionId": "4046613", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 3685}, {"functionName": "m0", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 1376}, {"functionName": "k4", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 6219}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Wn.request", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 9390}, {"functionName": "", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 57799}, {"functionName": "", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 3515}, {"functionName": "b", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 3485}, {"functionName": "Ko", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 5577}, {"functionName": "mutationFn", "scriptId": "44", "url": "https://hunhepan.com/assets/LoginForm-lKvH9Z3F.js", "lineNumber": 0, "columnNumber": 306}, {"functionName": "fn", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 16247}, {"functionName": "w", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 6435}, {"functionName": "start", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 6918}, {"functionName": "", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 17054}, {"functionName": "o", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1135}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "f", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1248}, {"functionName": "o", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1131}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "f", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1248}, {"functionName": "", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1258}, {"functionName": "ae", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 0, "columnNumber": 1097}, {"functionName": "execute", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 16118}, {"functionName": "mutate", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 36601}, {"functionName": "", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 39939}, {"functionName": "m", "scriptId": "44", "url": "https://hunhepan.com/assets/LoginForm-lKvH9Z3F.js", "lineNumber": 0, "columnNumber": 333}, {"functionName": "onSubmit", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 220438}, {"functionName": "", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 214048}, {"functionName": "", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 213782}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 213523}, {"functionName": "", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 208002}, {"functionName": "submit", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 213963}, {"functionName": "onSubmit", "scriptId": "20", "url": "https://hunhepan.com/assets/ui-CAmExcCJ.js", "lineNumber": 20, "columnNumber": 221161}, {"functionName": "U<PERSON>", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 9906}, {"functionName": "qy", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 10060}, {"functionName": "Yy", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 10117}, {"functionName": "If", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 31503}, {"functionName": "Qx", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 31920}, {"functionName": "", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 36833}, {"functionName": "vc", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 36896}, {"functionName": "Ex", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 9039}, {"functionName": "r0", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 33207}, {"functionName": "<PERSON>u", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 17414}, {"functionName": "lm", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 28, "columnNumber": 17198}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://hunhepan.com/v1/user/login", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "hunhepan.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/v1/user/login"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"name": "content-length", "value": "49"}, {"name": "content-type", "value": "application/json"}, {"name": "dnt", "value": "1"}, {"name": "origin", "value": "https://hunhepan.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://hunhepan.com/"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "x-uuid", "value": "1c2cdfcb-6526-4f66-974e-828b9b73e98e"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 49, "postData": {"mimeType": "application/json", "text": "{\"username\":\"albert0530\",\"password\":\"albert0530\"}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "https://hunhepan.com"}, {"name": "access-control-expose-headers", "value": "Content-Type,Authorization,X-Login,X-Uuid"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "cf-ray", "value": "944e55e07c3024d6-SJC"}, {"name": "content-encoding", "value": "zstd"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Sat, 24 May 2025 16:58:02 GMT"}, {"name": "nel", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"name": "report-to", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=TwYLGnYCW8m3Fku%2B6nPSOovZY6qV1%2FGipMXzno3Xu3GqiGxYztC9MDrYv%2FdmuxjvAoQimFCdHOXK%2BIehvIrkf0eq7RZfhpCVlQg%3D\"}]}"}, {"name": "server", "value": "cloudflare"}, {"name": "vary", "value": "Origin"}], "cookies": [], "content": {"size": 251, "mimeType": "application/json", "text": "{\"code\":200,\"msg\":\"请求成功\",\"data\":{\"id\":11439,\"role\":\"user\",\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMTQzOSwiaXNzIjoiaHVuaGVwYW4iLCJleHAiOjE3NDkxODU4ODJ9.a4N3iZ0tlRp_LJqhDnVsKbSVSwj8ZYd5K4DXoYyCiCQ\",\"username\":\"albert0530\"}}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 758, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-05-24T16:57:59.575Z", "time": 3453.9149999586343, "timings": {"blocked": 4.686999955654144, "dns": 0.030999999999999694, "ssl": 823.1120000000001, "connect": 1585.4740000000002, "send": 0.2819999999999254, "wait": 1862.4930000021905, "receive": 0.9480000007897615, "_blocked_queueing": 1.3089999556541443, "_blocked_proxy": 2.4810000000000003, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "4046613", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 3685}, {"functionName": "m0", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 1376}, {"functionName": "k4", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 6219}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Wn.request", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 32, "columnNumber": 9390}, {"functionName": "", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 57799}, {"functionName": "", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 3515}, {"functionName": "b", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 3485}, {"functionName": "mn", "scriptId": "17", "url": "https://hunhepan.com/assets/index-uHH5Jwh_.js", "lineNumber": 0, "columnNumber": 5678}, {"functionName": "o", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 10919}, {"functionName": "w", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 6435}, {"functionName": "start", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 6918}, {"functionName": "fetch", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 12248}, {"functionName": "or", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 33275}, {"functionName": "setOptions", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 28846}, {"functionName": "", "scriptId": "18", "url": "https://hunhepan.com/assets/reacts-cpiLaNCT.js", "lineNumber": 8, "columnNumber": 39125}, {"functionName": "os", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 24257}, {"functionName": "Rn", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 42318}, {"functionName": "vd", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 36565}, {"functionName": "Hr", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 29, "columnNumber": 3282}, {"functionName": "A2", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 41221}, {"functionName": "Kr", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 40215}, {"functionName": "qv", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 31, "columnNumber": 35672}, {"functionName": "E", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 16, "columnNumber": 1552}, {"functionName": "$", "scriptId": "19", "url": "https://hunhepan.com/assets/libs-WJg8p7sX.js", "lineNumber": 16, "columnNumber": 1911}], "parentId": {"id": "12", "debuggerId": "1260595128909880802.-4082599725153084397"}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "GET", "url": "https://hunhepan.com/auth/v1/user/info", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "hunhepan.com"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/auth/v1/user/info"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"name": "dnt", "value": "1"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://hunhepan.com/"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "x-uuid", "value": "1c2cdfcb-6526-4f66-974e-828b9b73e98e"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "cf-ray", "value": "944e55ec3cb024d6-SJC"}, {"name": "content-encoding", "value": "zstd"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Sat, 24 May 2025 16:58:03 GMT"}, {"name": "nel", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"name": "report-to", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=ePGkplXDrw4WYspq03QleZaW%2FSUBVbRBVdF6sHa0VM%2BAqYeO0glZq0xMwjox2VwKoocE8H8mS01PwWhc6OX4iuj94ihQ9UmhP14%3D\"}]}"}, {"name": "server", "value": "cloudflare"}, {"name": "x-logged", "value": "true"}], "cookies": [], "content": {"size": 186, "mimeType": "application/json", "text": "{\"code\":200,\"msg\":\"请求成功\",\"data\":{\"id\":11439,\"create_time\":\"2025-05-05T00:59:02+08:00\",\"update_time\":\"2025-05-05T00:59:02+08:00\",\"username\":\"albert0530\",\"role\":\"user\",\"edges\":{}}}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 405, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-05-24T16:58:03.041Z", "time": 1420.3809999162331, "timings": {"blocked": 2.4329999527484176, "dns": -1, "ssl": -1, "connect": -1, "send": 0.15999999999999992, "wait": 1415.96799995853, "receive": 1.820000004954636, "_blocked_queueing": 1.0639999527484179, "_blocked_proxy": 0.875, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "4046700", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "Jc", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 197, "columnNumber": 315}, {"functionName": "am", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 377, "columnNumber": 86}, {"functionName": "DM", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 786, "columnNumber": 226}, {"functionName": "aa.flush", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 794, "columnNumber": 83}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 791, "columnNumber": 346}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "aa.Jb", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 791, "columnNumber": 326}, {"functionName": "aa.add", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 793, "columnNumber": 505}, {"functionName": "aa.Ql", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 811, "columnNumber": 26}, {"functionName": "aa.Fp", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 810, "columnNumber": 487}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 807, "columnNumber": 483}, {"functionName": "hn", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 398, "columnNumber": 149}, {"functionName": "bp", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 415, "columnNumber": 67}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 807, "columnNumber": 467}, {"functionName": "c", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 586, "columnNumber": 7}, {"functionName": "<PERSON><PERSON>", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 586, "columnNumber": 38}, {"functionName": "aa.Ep", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 807, "columnNumber": 453}, {"functionName": "c", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 822, "columnNumber": 917}, {"functionName": "w", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 432, "columnNumber": 368}, {"functionName": "rn", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 400, "columnNumber": 512}, {"functionName": "Gq", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 432, "columnNumber": 451}, {"functionName": "Fq.flush", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 435, "columnNumber": 406}, {"functionName": "Fq.push", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 433, "columnNumber": 492}, {"functionName": "yq", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 429, "columnNumber": 539}, {"functionName": "event", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 640, "columnNumber": 412}, {"functionName": "pD", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 646, "columnNumber": 470}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 649, "columnNumber": 268}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "A", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 194, "columnNumber": 197}, {"functionName": "sw", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 649, "columnNumber": 251}, {"functionName": "ow.enqueue", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 548, "columnNumber": 988}, {"functionName": "pw", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 549, "columnNumber": 455}, {"functionName": "YO", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 834, "columnNumber": 44}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 303, "columnNumber": 186}, {"functionName": "aa.invoke", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 209, "columnNumber": 366}, {"functionName": "Na", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 926}, {"functionName": "Ma", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 682}, {"functionName": "zd", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 225, "columnNumber": 316}, {"functionName": "aa.invoke", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 209, "columnNumber": 366}, {"functionName": "Na", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 926}, {"functionName": "Ma", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 682}, {"functionName": "be", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 236, "columnNumber": 276}, {"functionName": "aa.invoke", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 209, "columnNumber": 366}, {"functionName": "Na", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 926}, {"functionName": "Ma", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 682}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 233, "columnNumber": 465}, {"functionName": "aa.invoke", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 209, "columnNumber": 366}, {"functionName": "Na", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 926}, {"functionName": "Ma", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 175, "columnNumber": 682}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 233, "columnNumber": 465}, {"functionName": "aa.invoke", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 209, "columnNumber": 366}, {"functionName": "r", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 213, "columnNumber": 125}, {"functionName": "jF", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 678, "columnNumber": 179}, {"functionName": "lF", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 678, "columnNumber": 275}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 677, "columnNumber": 113}, {"functionName": "", "scriptId": "24", "url": "https://www.googletagmanager.com/gtag/js?id=G-6L8PM2M8FQ", "lineNumber": 676, "columnNumber": 77}]}}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "7890", "request": {"method": "POST", "url": "https://www.google-analytics.com/g/collect?v=2&tid=G-6L8PM2M8FQ&gtm=45je55l1v9132591216za200&_p=1748105845740&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=101509157~102938614~103116026~103130498~103130500~103200004~103233427~103252644~103252646~104481633~104481635&cid=887643501.1740931166&ul=zh-cn&sr=1800x1169&uaa=arm&uab=64&uafvl=Google%2520Chrome%3B135.0.7049.115%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.115&uamb=0&uam=&uap=macOS&uapv=15.4.1&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AEEAAAQ&_s=3&sid=1748105846&sct=9&seg=1&dl=https%3A%2F%2Fhunhepan.com%2F&dt=%E6%B7%B7%E5%90%88%E7%9B%98%20-%20%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%84%E5%88%99%E7%B1%BBAPP%20-%20%E5%8F%AF%E6%90%9C%E7%B4%A2%E5%85%A8%E7%BD%91%E7%BD%91%E7%9B%98%E3%80%81%E7%A3%81%E5%8A%9B%E8%B5%84%E6%BA%90&en=form_start&ep.form_id=&ep.form_name=&ep.form_destination=https%3A%2F%2Fhunhepan.com%2F&epn.form_length=3&ep.first_field_id=username_input&ep.first_field_name=&ep.first_field_type=&epn.first_field_position=1&_et=24683&tfd=48008", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "www.google-analytics.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/g/collect?v=2&tid=G-6L8PM2M8FQ&gtm=45je55l1v9132591216za200&_p=1748105845740&gcd=13l3l3l3l1l1&npa=0&dma=0&tag_exp=101509157~102938614~103116026~103130498~103130500~103200004~103233427~103252644~103252646~104481633~104481635&cid=887643501.1740931166&ul=zh-cn&sr=1800x1169&uaa=arm&uab=64&uafvl=Google%2520Chrome%3B135.0.7049.115%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.115&uamb=0&uam=&uap=macOS&uapv=15.4.1&uaw=0&are=1&frm=0&pscdl=noapi&_eu=AEEAAAQ&_s=3&sid=1748105846&sct=9&seg=1&dl=https%3A%2F%2Fhunhepan.com%2F&dt=%E6%B7%B7%E5%90%88%E7%9B%98%20-%20%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%84%E5%88%99%E7%B1%BBAPP%20-%20%E5%8F%AF%E6%90%9C%E7%B4%A2%E5%85%A8%E7%BD%91%E7%BD%91%E7%9B%98%E3%80%81%E7%A3%81%E5%8A%9B%E8%B5%84%E6%BA%90&en=form_start&ep.form_id=&ep.form_name=&ep.form_destination=https%3A%2F%2Fhunhepan.com%2F&epn.form_length=3&ep.first_field_id=username_input&ep.first_field_name=&ep.first_field_type=&epn.first_field_position=1&_et=24683&tfd=48008"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"name": "content-length", "value": "0"}, {"name": "dnt", "value": "1"}, {"name": "origin", "value": "https://hunhepan.com"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://hunhepan.com/"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "no-cors"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "sec-fetch-storage-access", "value": "active"}, {"name": "user-agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}], "queryString": [{"name": "v", "value": "2"}, {"name": "tid", "value": "G-6L8PM2M8FQ"}, {"name": "gtm", "value": "45je55l1v9132591216za200"}, {"name": "_p", "value": "1748105845740"}, {"name": "gcd", "value": "13l3l3l3l1l1"}, {"name": "npa", "value": "0"}, {"name": "dma", "value": "0"}, {"name": "tag_exp", "value": "101509157~102938614~103116026~103130498~103130500~103200004~103233427~103252644~103252646~104481633~104481635"}, {"name": "cid", "value": "887643501.1740931166"}, {"name": "ul", "value": "zh-cn"}, {"name": "sr", "value": "1800x1169"}, {"name": "uaa", "value": "arm"}, {"name": "uab", "value": "64"}, {"name": "uafvl", "value": "Google%2520Chrome%3B135.0.7049.115%7CNot-A<PERSON>%3B8.0.0.0%7CChromium%3B135.0.7049.115"}, {"name": "uamb", "value": "0"}, {"name": "uam", "value": ""}, {"name": "uap", "value": "macOS"}, {"name": "uapv", "value": "15.4.1"}, {"name": "uaw", "value": "0"}, {"name": "are", "value": "1"}, {"name": "frm", "value": "0"}, {"name": "pscdl", "value": "no<PERSON>i"}, {"name": "_eu", "value": "AEEAAAQ"}, {"name": "_s", "value": "3"}, {"name": "sid", "value": "1748105846"}, {"name": "sct", "value": "9"}, {"name": "seg", "value": "1"}, {"name": "dl", "value": "https%3A%2F%2Fhunhepan.com%2F"}, {"name": "dt", "value": "%E6%B7%B7%E5%90%88%E7%9B%98%20-%20%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%84%E5%88%99%E7%B1%BBAPP%20-%20%E5%8F%AF%E6%90%9C%E7%B4%A2%E5%85%A8%E7%BD%91%E7%BD%91%E7%9B%98%E3%80%81%E7%A3%81%E5%8A%9B%E8%B5%84%E6%BA%90"}, {"name": "en", "value": "form_start"}, {"name": "ep.form_id", "value": ""}, {"name": "ep.form_name", "value": ""}, {"name": "ep.form_destination", "value": "https%3A%2F%2Fhunhepan.com%2F"}, {"name": "epn.form_length", "value": "3"}, {"name": "ep.first_field_id", "value": "username_input"}, {"name": "ep.first_field_name", "value": ""}, {"name": "ep.first_field_type", "value": ""}, {"name": "epn.first_field_position", "value": "1"}, {"name": "_et", "value": "24683"}, {"name": "tfd", "value": "48008"}], "cookies": [], "headersSize": -1, "bodySize": 0}, "response": {"status": 204, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "https://hunhepan.com"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "cache-control", "value": "no-cache, no-store, must-revalidate"}, {"name": "content-length", "value": "0"}, {"name": "content-security-policy-report-only", "value": "script-src 'none'; form-action 'none'; frame-src 'none'; report-uri https://csp.withgoogle.com/csp/scaffolding/ascnsrsggc:99:0"}, {"name": "content-type", "value": "text/plain"}, {"name": "cross-origin-opener-policy-report-only", "value": "same-origin; report-to=ascnsrsggc:99:0"}, {"name": "cross-origin-resource-policy", "value": "cross-origin"}, {"name": "date", "value": "Sat, 24 May 2025 16:58:04 GMT"}, {"name": "expires", "value": "Fri, 01 Jan 1990 00:00:00 GMT"}, {"name": "pragma", "value": "no-cache"}, {"name": "report-to", "value": "{\"group\":\"ascnsrsggc:99:0\",\"max_age\":2592000,\"endpoints\":[{\"url\":\"https://csp.withgoogle.com/csp/report-to/scaffolding/ascnsrsggc:99:0\"}],}"}, {"name": "server", "value": "Golfe2"}], "cookies": [], "content": {"size": 0, "mimeType": "text/plain"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 552, "_error": "net::ERR_ABORTED", "_fetchedViaServiceWorker": false}, "serverIPAddress": "127.0.0.1", "startedDateTime": "2025-05-24T16:58:03.382Z", "time": 394.5450000464916, "timings": {"blocked": 3.556000030629337, "dns": -1, "ssl": 244.56, "connect": 245.23899999999998, "send": 0.6370000000000005, "wait": 143.039999947086, "receive": 2.0730000687763095, "_blocked_queueing": 2.863000030629337, "_blocked_proxy": 0.16700000000000004, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}