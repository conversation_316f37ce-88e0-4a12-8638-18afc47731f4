#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sqlite3
from resource_submitters.resource_submit_manager import ResourceSubmitManager
from utils import custom_print

async def submit_to_sroad():
    """提交所有未提交到Sroad站点的记录"""
    # 初始化资源提交管理器
    config = {
        "enabled": True,
        "auto_submit": True,
        "submitters": ["sroad"],
        "submit_delay": {
            "min": 1,
            "max": 2
        },
        "avoid_duplicate": True,
        "sroad": {
            "enabled": True,
            "api_url": "https://hk10g.sroad.win/sub",
            "batch_size": 10,
            "use_proxy": False
        }
    }
    
    resource_submit_manager = ResourceSubmitManager(config)
    
    # 提交未提交的记录
    custom_print("=" * 50)
    custom_print("开始提交未提交到Sroad的分享记录...")
    custom_print("=" * 50)
    
    result = await resource_submit_manager.submit_from_database(limit=100)
    
    if result["code"] == 0:
        custom_print(f"成功: {result['message']}")
        
        # 显示详细统计信息
        details = result.get("details", {})
        if details:
            for submitter_type, submitter_result in details.items():
                status = "成功" if submitter_result.get("code") == 0 else "失败"
                message = submitter_result.get("message", "无消息")
                custom_print(f"{submitter_type} 提交结果: {status} - {message}")
                
                # 显示每批次的详细信息
                batch_details = submitter_result.get("details", [])
                if batch_details:
                    custom_print(f"  批次数量: {len(batch_details)}")
                    for i, batch in enumerate(batch_details):
                        batch_code = batch.get("code", -1)
                        batch_msg = batch.get("message", "无消息")
                        batch_links = batch.get("links", [])
                        batch_response = batch.get("response", {})
                        
                        custom_print(f"  批次 {i+1}:")
                        custom_print(f"    - 状态: {'成功' if batch_code == 0 else '失败'}")
                        custom_print(f"    - 消息: {batch_msg}")
                        custom_print(f"    - 链接数: {len(batch_links)}")
                        
                        # 显示原始响应内容
                        if batch_response:
                            custom_print("    - 服务器原始响应:")
                            if isinstance(batch_response, dict):
                                # 显示原始响应文本
                                raw_text = batch_response.get("raw_text", None)
                                if raw_text:
                                    custom_print(f"      {raw_text}")
    else:
        custom_print(f"失败: {result['message']}", error_msg=True)
    
    custom_print("=" * 50)
    
    return result

def reset_sroad_submit_records():
    """重置Sroad提交记录，将所有记录标记为未提交"""
    try:
        # 连接数据库
        conn = sqlite3.connect('data/quark_links.db')
        cursor = conn.cursor()
        
        # 检查submit_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            custom_print("提交记录表不存在，无需重置", error_msg=True)
            conn.close()
            return False
        
        # 删除所有Sroad的提交记录
        cursor.execute("DELETE FROM submit_records WHERE submitter_type = 'sroad'")
        conn.commit()
        
        deleted_count = cursor.rowcount
        custom_print(f"已重置 {deleted_count} 条Sroad提交记录")
        
        conn.close()
        return True
    except Exception as e:
        custom_print(f"重置Sroad提交记录失败: {e}", error_msg=True)
        return False

if __name__ == "__main__":
    # 询问是否重置提交记录
    reset_option = input("是否重置Sroad提交记录？(y/n): ")
    if reset_option.lower() == 'y':
        reset_sroad_submit_records()
    
    # 提交未提交的记录
    asyncio.run(submit_to_sroad())
