#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import httpx
import json
import os
import sys
from utils import custom_print

async def test_share_stats(cookies=None):
    """测试分享统计API"""
    if not cookies:
        custom_print("未提供cookies，请先登录夸克网盘", error_msg=True)
        return

    # 设置请求头
    headers = {
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
        'origin': 'https://pan.quark.cn',
        'referer': 'https://pan.quark.cn/',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cookie': cookies,
    }

    # 分享统计API URL
    url = "https://drive-pc.quark.cn/1/clouddrive/share/mypage/detail"

    # 查询参数
    params = {
        "pr": "ucpro",
        "fr": "pc",
        "uc_param_str": "",
        "_page": "1",
        "_size": "50",
        "_order_field": "created_at",
        "_order_type": "desc",
        "_fetch_total": "1",
        "_fetch_notify_follow": "1"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params)

            # 检查响应状态码
            if response.status_code != 200:
                custom_print(f"请求失败，状态码: {response.status_code}", error_msg=True)
                return

            # 解析响应JSON
            try:
                data = response.json()
            except Exception as e:
                custom_print(f"解析响应JSON失败: {e}", error_msg=True)
                return

            # 检查响应是否成功
            if data.get("status") != 200:
                custom_print(f"API返回错误: {data.get('message', '未知错误')}", error_msg=True)
                return

            # 打印响应数据结构
            custom_print("API响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))

            # 分析响应中是否包含分享统计数据
            if "data" in data and "list" in data["data"]:
                shares = data["data"]["list"]
                custom_print(f"找到 {len(shares)} 条分享记录")

                # 检查是否包含浏览统计数据
                has_view_stats = False
                has_download_stats = False

                # 分析第一条记录的字段
                if shares:
                    first_share = shares[0]
                    custom_print("\n第一条分享记录的字段:")
                    for key, value in first_share.items():
                        print(f"  {key}: {value}")

                        # 检查是否包含浏览统计相关字段
                        if "view" in key.lower() or "visit" in key.lower() or "count" in key.lower():
                            has_view_stats = True

                        # 检查是否包含下载统计相关字段
                        if "download" in key.lower():
                            has_download_stats = True

                # 输出分析结果
                custom_print("\n分析结果:")
                if has_view_stats:
                    custom_print("✅ 响应中包含浏览统计数据")
                else:
                    custom_print("❌ 响应中不包含明确的浏览统计数据", error_msg=True)

                if has_download_stats:
                    custom_print("✅ 响应中包含下载统计数据")
                else:
                    custom_print("❌ 响应中不包含明确的下载统计数据", error_msg=True)

                # 提取所有分享记录的关键信息
                custom_print("\n分享记录摘要:")
                for i, share in enumerate(shares[:5], 1):  # 只显示前5条
                    share_id = share.get("share_id", "未知")
                    title = share.get("title", "未知")
                    created_at = share.get("created_at", "未知")

                    # 尝试提取可能的统计数据
                    view_count = share.get("view_count", share.get("visit_count", "未知"))
                    download_count = share.get("download_count", "未知")

                    custom_print(f"{i}. 分享ID: {share_id}, 标题: {title}, 创建时间: {created_at}")
                    custom_print(f"   浏览次数: {view_count}, 下载次数: {download_count}")
            else:
                custom_print("响应中没有找到分享记录列表", error_msg=True)

    except Exception as e:
        custom_print(f"请求过程中出错: {e}", error_msg=True)

# 从配置文件中读取cookies
def get_cookies_from_config():
    try:
        config_path = 'config/config.json'
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("cookies", "")
        return ""
    except Exception as e:
        custom_print(f"读取配置文件失败: {e}", error_msg=True)
        return ""

if __name__ == "__main__":
    # 导入QuarkPanFileManager类
    try:
        from quark import QuarkPanFileManager

        # 创建QuarkPanFileManager实例
        quark_file_manager = QuarkPanFileManager(headless=True, slow_mo=500)

        # 获取cookies
        cookies = quark_file_manager.cookies

        if not cookies:
            custom_print("未找到cookies，请先登录夸克网盘", error_msg=True)
            sys.exit(1)

        custom_print(f"成功获取cookies，长度: {len(cookies)}")
        asyncio.run(test_share_stats(cookies))
    except Exception as e:
        custom_print(f"初始化QuarkPanFileManager失败: {e}", error_msg=True)
        sys.exit(1)
