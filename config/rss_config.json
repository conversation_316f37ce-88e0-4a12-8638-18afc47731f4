{"sources": [{"url": "rsshub://telegram/channel/XiangxiuNB", "type": "rss"}, {"url": "rsshub://telegram/channel/yunpanpan", "type": "rss"}, {"url": "https://linux.do/tag/%E5%BD%B1%E8%A7%86.rss", "type": "rss"}, {"url": "rsshub://telegram/channel/Aliyun_4K_Movies", "type": "rss"}, {"url": "rsshub://telegram/channel/dianying4K", "type": "rss"}, {"url": "https://doc.bilistudy.com/17", "type": "rss"}, {"url": "rsshub://telegram/channel/Quark_Movies", "type": "rss"}, {"url": "https://bbs.yiove.com/forum-4.htm?rss=1", "type": "rss"}, {"url": "rsshub://telegram/channel/wpzyk", "type": "rss"}, {"url": "rsshub://telegram/channel/NewQuark", "type": "rss"}], "rss_sources": ["rsshub://telegram/channel/XiangxiuNB", "rsshub://telegram/channel/yunpanpan", "https://linux.do/tag/%E5%BD%B1%E8%A7%86.rss", "rsshub://telegram/channel/Aliyun_4K_Movies", "rsshub://telegram/channel/dianying4K", "rsshub://telegram/channel/Quark_Movies", "https://bbs.yiove.com/forum-4.htm?rss=1", "rsshub://telegram/channel/wpzyk", "rsshub://telegram/channel/NewQuark"], "auto_run_interval": 7500, "quark_link_patterns": ["https://pan.quark.cn/s/[0-9a-f]{12,16}", "https://pan.quark.cn/s/[0-9a-zA-Z]{12,16}"], "proxy": {"enabled": false, "http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890", "socks5": "socks5://127.0.0.1:7890"}, "fetch_article_content": true, "auto_share": true, "share_settings": {"url_type": 1, "expired_type": 4, "password": ""}, "resource_submit": {"enabled": true, "auto_submit": true, "submitters": ["krzb", "yunso", "buyutu", "sroad", "yiso", "<PERSON><PERSON><PERSON>"], "submit_delay": {"min": 1, "max": 5}, "check_limit": 100, "avoid_duplicate": true, "krzb": {"enabled": true, "api_url": "https://fc-resource-node-api.krzb.net", "batch_size": 10, "use_proxy": false}, "yiso": {"enabled": false, "api_url": "https://yiso.fun/api/member/share", "batch_size": 1, "use_proxy": false}, "yunso": {"enabled": true, "api_url": "https://www.yunso.net/api/validate/dataentry.html", "login_url": "https://dash.yunso.net/index/user/login.html", "dataentry_url": "https://www.yunso.net/index/user/dataentry", "check_login_url": "https://www.yunso.net/index/user/index.html", "batch_size": 10, "use_proxy": false, "publicity": "1", "common_headers": {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "DNT": "1"}, "login_credentials": {"url": "https://dash.yunso.net/index/user/index.html", "account": "<PERSON>bert", "password": "xWumeC47q9LX2LF", "keeplogin": "1"}}, "buyutu": {"enabled": true, "api_url": "https://www.buyutu.com/sub", "batch_size": 10, "use_proxy": false, "retry_count": 3, "delay": {"min": 1.0, "max": 3.0}, "random_ua": true, "headers": {"accept": "application/json, text/javascript, */*; q=0.01", "accept-language": "zh-CN,zh;q=0.9,en;q=0.8", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "priority": "u=1, i", "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "same-origin", "x-requested-with": "XMLHttpRequest", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, "sroad": {"enabled": true, "api_url": "https://hk10g.sroad.win/sub", "batch_size": 10, "use_proxy": false, "retry_count": 3, "delay": {"min": 1.0, "max": 3.0}, "random_ua": true, "headers": {"accept": "application/json, text/javascript, */*; q=0.01", "accept-language": "zh-CN,zh;q=0.9,en;q=0.8", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "priority": "u=1, i", "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "same-origin", "x-requested-with": "XMLHttpRequest", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, "macyeah": {"enabled": true, "login_url": "https://************/login", "submit_url": "https://************/feedback", "batch_size": 8, "use_proxy": false, "timeout": 60, "retry_times": 3, "retry_interval": 5, "verify_ssl": false, "login_credentials": {"email": "<EMAIL>", "password": "albert4417"}, "common_headers": {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}}}}