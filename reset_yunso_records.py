"""
重置云搜网(站点二)的提交记录
将所有在submit_records表中标记为已提交到yunso的记录重置为未提交
"""
import os
import sqlite3
from typing import List, Dict, Any, Optional, Tuple

from utils import custom_print, get_datetime


def reset_yunso_submit_records(db_path: str = 'data/quark_links.db') -> bool:
    """
    重置云搜网(站点二)的提交记录
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 是否成功重置记录
    """
    try:
        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        os.makedirs(db_dir, exist_ok=True)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查submit_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='submit_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            custom_print("提交记录表不存在，无需重置", error_msg=True)
            conn.close()
            return False
        
        # 查询yunso提交记录数量
        cursor.execute(
            "SELECT COUNT(*) FROM submit_records WHERE submitter_type = 'yunso'"
        )
        count = cursor.fetchone()[0]
        
        if count == 0:
            custom_print("没有云搜网提交记录，无需重置")
            conn.close()
            return True
        
        custom_print(f"找到 {count} 条云搜网提交记录")
        
        # 删除yunso提交记录
        cursor.execute(
            "DELETE FROM submit_records WHERE submitter_type = 'yunso'"
        )
        
        conn.commit()
        conn.close()
        
        custom_print(f"成功删除 {count} 条云搜网提交记录")
        return True
    except Exception as e:
        custom_print(f"重置云搜网提交记录失败: {e}", error_msg=True)
        return False


def run_reset():
    """运行重置操作"""
    custom_print("=" * 50)
    custom_print("开始重置云搜网提交记录")
    custom_print("=" * 50)
    
    if reset_yunso_submit_records():
        custom_print("云搜网提交记录重置成功")
    else:
        custom_print("云搜网提交记录重置失败", error_msg=True)
    
    custom_print("=" * 50)


if __name__ == "__main__":
    run_reset()
