    async def submit_task(self, task_id: str, retry: int = 50) -> Union[
                bool, Dict[str, Union[str, Dict[str, Union[int, str]]]]]:

        for i in range(retry):
            # 随机暂停100-50毫秒
            await asyncio.sleep(random.randint(500, 1000) / 1000)
            custom_print(f'第{i + 1}次提交任务')
            submit_url = (f"https://drive-pc.quark.cn/1/clouddrive/task?pr=ucpro&fr=pc&uc_param_str=&task_id={task_id}"
                          f"&retry_index={i}&__dt=21192&__t={get_timestamp(13)}")

            # 使用带重试机制的客户端
            client_with_retry = await self.get_client_with_retry()
            try:
                response = await client_with_retry.request('get', submit_url, headers=self.headers)
                json_data = response.json()
                
                if json_data['message'] == 'ok':
                    if json_data['data']['status'] == 2:
                        if 'to_pdir_name' in json_data['data']['save_as']:
                            folder_name = json_data['data']['save_as']['to_pdir_name']
                        else:
                            folder_name = ' 根目录'
                        if json_data['data']['task_title'] == '分享-转存':
                            custom_print(f"结束任务ID：{task_id}")
                            custom_print(f'文件保存位置：{folder_name} 文件夹')
                        return json_data
                else:
                    if json_data['code'] == 32003 and 'capacity limit' in json_data['message']:
                        custom_print("转存失败，网盘容量不足！请注意当前已成功保存的个数，避免重复保存", error_msg=True)
                    elif json_data['code'] == 41013:
                        custom_print(f"网盘文件夹不存在，请重新运行按3切换保存目录后重试！", error_msg=True)
                    else:
                        custom_print(f"错误信息：{json_data['message']}", error_msg=True)
                    input(f'[{get_datetime()}] 已退出程序')
                    sys.exit()
            except Exception as e:
                custom_print(f"提交任务失败: {e}，尝试重试", error_msg=True)
                continue
